package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.repository.*;
import com.magnamedia.service.DirectDebitService;
import com.magnamedia.service.FlowProcessorService;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.OneMonthAgreementFlowService;
import org.joda.time.LocalDate;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

//ACC-4905
public class OneMonthAgreementFlowMessagesJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(OneMonthAgreementFlowMessagesJob.class.getName());

    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    private ContractPaymentTermRepository contractPaymentTermRepository;
    private DirectDebitRepository directDebitRepository;
    private ContractService contractService;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Started job");
        oneMonthAgreementFlowService = Setup.getApplicationContext().getBean(OneMonthAgreementFlowService.class);
        contractPaymentTermRepository = Setup.getRepository(ContractPaymentTermRepository.class);
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        contractService = Setup.getApplicationContext().getBean(ContractService.class);

        flagOMAContractAsPayingViaCreditCard();
//        sendOneMonthAgreementMessages();
        checkClientPayingDdIfPassedProratedDate();

        logger.log(Level.INFO, "Ended job");
    }

//    private void sendOneMonthAgreementMessages() {
//
//        List<FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
//                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);
//        logger.log(Level.SEVERE, "flowProcessorEntityRepository size: {0}", flowProcessorEntities.size());
//
//        for (FlowProcessorEntity entity : flowProcessorEntities) {
//            try {
//                logger.log(Level.INFO, "processing entity id: {0}", entity.getId());
//
//                if (!entity.getContract().isOneMonthAgreement()) {
//                    logger.log(Level.INFO, "Flag changed to false -> stop the flow id: {0}", entity.getId());
//                    entity.setStopped(true);
//                    flowProcessorEntityRepository.save(entity);
//                    continue;
//                }
//
//                if(oneMonthAgreementFlowService.resetFlow(entity)) {
//                    logger.log(Level.INFO, "Payment received reset entity id: {0}", entity.getId());
//                    continue;
//                }
//
//                DateTime paidEndDate = new DateTime(entity.getContract().getPaidEndDate());
//                if (new DateTime().isAfter(paidEndDate)) {
//                    entity.setTrials(entity.getCurrentSubEvent().getMaxTrials() - 1);
//                    entity.setReminders(entity.getCurrentSubEvent().getMaxReminders());
//                }
//
//                flowProcessorService.processFlowSubEventConfig(entity, entity.getCurrentSubEvent());
//
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

    // ACC-6647
    private void flagOMAContractAsPayingViaCreditCard() {

        for (ContractPaymentTerm cpt : contractPaymentTermRepository.findOneMonthAgreementPayingViaCreditCard()) {
            try {
                contractService.fixAdjustedEndDate(cpt.getContract(), false);

                // ACC-5587 #2
                if (new LocalDate(cpt.getContract().getStartOfContract()).toString("yyyy-MM-dd")
                        .equals(new LocalDate(cpt.getContract().getPaidEndDate()).toString("yyyy-MM-dd")))
                    continue;

                Setup.getApplicationContext()
                        .getBean(ContractService.class)
                        .updatePayingViaCreditCardFlag(cpt.getContract(), true);


            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void checkClientPayingDdIfPassedProratedDate() {

        Date d = new LocalDate().toDate();
        contractPaymentTermRepository.findOneMonthAgreementPayingViaDd(d)
                .forEach(cpt -> {

                    logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
                    oneMonthAgreementFlowService.flowStoppedResetFlag(cpt.getContract());

                    if (directDebitRepository
                            .existsProratedDdAndStartDateAndStatusNotInOrHasPaymentReceived(
                                    cpt.getId(), d, DirectDebitService.notAllowedStatuses, cpt.getContract().getId()))
                        return;

                    sendEmailForNoDdCoverProratedPayment(cpt.getContract().getClient());
                });
    }

    private void sendEmailForNoDdCoverProratedPayment(Client c) {

        logger.log(Level.INFO, "client id: {0}", c.getId());

        String subject = "No prorated DD for one-month client paying via DD (" + c.getName() + ")";
        String baseURL = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_FRONT_END_URL) + "#!/client/details/";
        Map<String, String> parameters = new HashMap<String, String>(){{
            put("link", baseURL + c.getId());
            put("client_name", c.getName());
        }};

        Setup.getApplicationContext()
                .getBean(MessagingService.class).
                sendEmailToOfficeStaff(
                        "no_prorated_dd_for_one_month_agreement", parameters,
                        Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_NO_PRORATED_DD_FOR_ONE_MONTH_AGREEMENT_EMAIL_RECEIPTS),
                        subject);
    }
}