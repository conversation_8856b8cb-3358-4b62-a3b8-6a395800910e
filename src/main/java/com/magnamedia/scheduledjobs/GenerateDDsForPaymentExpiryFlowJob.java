package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import java.util.*;
import java.util.logging.Logger;

/*

 * <AUTHOR>
 * @created 14/03/2024 - 1:13 PM
 * ACC-6936 ACC-7525

 */
public class GenerateDDsForPaymentExpiryFlowJob implements MagnamediaJob {


    private static final Logger logger = Logger.getLogger(GenerateDDsForPaymentExpiryFlowJob.class.getName());
    private FlowEventConfig flowEventConfig;
    private FlowProcessorService flowProcessorService;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    private DirectDebitSignatureService directDebitSignatureService;
    private FlowSubEventConfig flowSubEventConfig;
    private DirectDebitService directDebitService;
    private DirectDebitRepository directDebitRepository;
    private InterModuleConnector interModuleConnector;
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;

    @Override
    public void run(Map<String, ?> map) {
        logger.info("Start job");
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW);
        flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.GENERATE_DDS_AFTER_PAID_END_DATE,
                        flowEventConfig);
        directDebitService = Setup.getApplicationContext().getBean(DirectDebitService.class);
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        interModuleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);
        calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);

        if (flowEventConfig == null) return;
        logger.info( "flowEventConfig id: " + flowEventConfig.getId());

        createPaymentExpiryFlow();
        paymentExpiryFlowTermination();
        logger.info("End job");
    }

    private void createPaymentExpiryFlow() {

        contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);
        directDebitSignatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);
        ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);
        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        List<DirectDebitStatus> notAllowedStatuses = Arrays.asList(
                DirectDebitStatus.CANCELED,
                DirectDebitStatus.REJECTED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED);
        Long lastId = -1L;
        Page<Contract> contracts;

        do {
            int xDays = Integer.parseInt(Setup.getParameter(
                    Setup.getCurrentModule(), AccountingModule.PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD));
            contracts = contractRepository.findForPaymentExpiryFlow(
                    lastId,
                    new LocalDate().toDate(),
                    new LocalDate().plusDays(xDays).toDate(),
                    Arrays.asList(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW,
                            FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED,
                            FlowEventConfig.FlowEventName.EXTENSION_FLOW),
                    PageRequest.of(0, 200));

            contracts.getContent().forEach(contract -> {
                try {
                    logger.info( "contract id : " + contract.getId());

                    if (directDebitRepository.existsMonthlyDdCoverAfterPaidEndDate(contract,
                            new LocalDate(contract.getPaidEndDate())
                                    .plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                            notAllowedStatuses)) {
                        logger.info("contract has active monthly dd -> exiting");
                        return;
                    }

                    if (directDebitRepository.existsRejectionFlowOfMonthlyDdCoverAfterPaidEndDate(contract,
                            new LocalDate(contract.getPaidEndDate())
                                    .plusMonths(1).dayOfMonth().withMinimumValue().toDate())) {
                        logger.info("contract has rejection flow -> exiting");
                        return;
                    }

                    if (new LocalDate(contract.getStartOfContract()).toString("yyyy-MM-dd")
                            .equals(new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd")) ||
                            directDebitService.contractHasOpenMainDdcToDo(contract.getId()) ) {
                        logger.info("contract has open DDC -> exiting");
                        return;
                    }

                    // ACC-7525 Contract has at least 1 closed DDC to-do in its log OR created the contract via CC payments
                    if (!interModuleConnector.get("/sales/appsserviceddapprovaltodo/contracthastodo?" +
                            "contract=" + contract.getId() + "&isClosed=true", Boolean.class)) {
                        logger.info("Contract has not closed DDC -> exiting");
                        return;
                    }

                    ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                    Map<String, Object> lastSignatureType = directDebitSignatureService
                            .getLastSignatureType(cpt, false, false);

                    boolean useOldSignatures = ((Boolean) lastSignatureType.get("useApprovedSignature") ||
                            (Boolean) lastSignatureType.get("useNonRejectedSignature"));

                    Map<String, Object> additionalInfo = generateDdsForPaymentExpiryFlow(cpt, monthlyPayment, useOldSignatures);

                    startPaymentExpiryFlow(cpt,
                            new HashMap<String, Object>() {{
                                put("additionalInfo", additionalInfo);
                            }},
                            useOldSignatures && directDebitService.isRequiredBankInfoExist(cpt));
                }
                catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!contracts.getContent().isEmpty()) {
                lastId = contracts.getContent().get(contracts.getContent().size() - 1).getId();
            }
        }
        while (!contracts.getContent().isEmpty());
    }

    private Map<String, Object> generateDdsForPaymentExpiryFlow(
            ContractPaymentTerm cpt, PicklistItem monthlyPayment, boolean useOldSignatures) throws Exception {

        Map<String, Object> additionalInfo = new HashMap<>();
        LocalDate ddaStartDate = new LocalDate(cpt.getContract().getPaidEndDate())
                .plusMonths(1).dayOfMonth().withMinimumValue();
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));

        DirectDebit firstDda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.toDate(),
                ddaStartDate.plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.toDate()), null,
                DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                true, true, true, null, true);
        additionalInfo.put("firstDdaId", firstDda.getId());

        DirectDebit secondDda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.plusMonths(1).toDate(),
                ddaStartDate.plusMonths(1).plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.plusMonths(1).toDate()), null,
                DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                true, true, true, null, true);
        additionalInfo.put("secondDdaId", secondDda.getId());

        LocalDate ddbStartDate = ddaStartDate.plusMonths(2);
        LocalDate ddbEndDate = ddbStartDate.plusMonths(Setup.getApplicationContext()
                .getBean(SwitchingNationalityService.class).getDefaultPaymentsDuration(cpt.getContract()) - 2);

        DirectDebit ddb = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddbStartDate.toDate(), ddbEndDate.toDate(),
                null, null, null, null, null,
                DirectDebitType.MONTHLY, null, useOldSignatures, null,
                false, true, true, null, true);
        additionalInfo.put("ddbId", ddb.getId());

        return additionalInfo;
    }

    private void startPaymentExpiryFlow(ContractPaymentTerm cpt, Map<String, Object> map, boolean bankInfoAndSignaturesAvailable) {

        logger.info( "start payment expiry flow for cpt id: " + cpt.getId());

        map.put("trials", 0);
        map.put("reminders", 1);
        FlowProcessorEntity f = flowProcessorService.createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);

        if (bankInfoAndSignaturesAvailable) {
            f.setCompleted(true);
            flowProcessorEntityRepository.save(f);
        }
    }

    private void paymentExpiryFlowTermination() {

        ContractService contractService = Setup.getApplicationContext()
                .getBean(ContractService.class);
        for (FlowProcessorEntity f : flowProcessorEntityRepository
                .findByFlowEventConfig_NameAndStoppedFalseAndCompletedFalse(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW)) {
            try {
                logger.info("flow id: " + f.getId());

                if (flowProcessorService.clientProvidesSignatureAndBankInfo(f.getContract(), f.getCreationDate())) {
                    f.setCompleted(true);
                    flowProcessorEntityRepository.save(f);
                    logger.info("Flow stopped because client provided signatures");
                    continue;
                }

                if (new DateTime().isAfter(new DateTime(f.getContract().getPaidEndDate()))) {

                    // ACC-8725
                    // Check if the flow related with any Hidden Direct Debit -> Stop Termination of Contract
                    if (f.getRelatedDirectDebits()
                            .stream()
                            .anyMatch(DirectDebit::isHidden)) {

                        logger.info("The Flow related with a Direct Debit Hidden -> exiting");
                        continue;
                    }

                    // Termination of Contract
                    contractService.setContractForTermination(f.getContract(), f.getFlowEventConfig().getCancellationReason().getCode());
                    f.setCausedTermination(true);
                    f.setStopped(true);
                    flowProcessorEntityRepository.save(f);
                    logger.info("Flow stopped because client didn't provide signatures");
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}