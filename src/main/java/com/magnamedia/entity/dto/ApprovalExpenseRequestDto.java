package com.magnamedia.entity.dto;

import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;

import java.util.Date;
import java.util.Map;

/**
 * Lightweight DTO for approval expense requests to avoid performance issues
 * with full entity serialization and N+1 queries
 */
public class ApprovalExpenseRequestDto {
    
    private Long id;
    private String token;
    private Double amount;
    private Double amountInLocalCurrency;
    private ExpensePaymentMethod paymentMethod;
    private ExpenseRequestStatus status;
    private Date creationDate;
    private String description;
    private String notes;
    private Double loanAmount;
    private String rejectionNotes;
    private Boolean canBeRefunded;
    private Long relatedToId;
    private Long beneficiaryId;
    private String beneficiaryMobileNumber;
    private String beneficiaryAccountName;
    private String beneficiaryIban;
    private String swift;
    private Boolean isRefunded;
    private String entityType;
    
    // Simplified nested objects
    private Map<String, Object> expense;
    private Map<String, Object> currency;
    private Map<String, Object> requestedBy;
    private Map<String, Object> purposeAdditionalDescription;
    private Map<String, Object> payment;
    private Map<String, Object> bucket;
    
    // Constructor for JPQL projection
    public ApprovalExpenseRequestDto(Long id, String token, Double amount, Double amountInLocalCurrency,
                                   ExpensePaymentMethod paymentMethod, ExpenseRequestStatus status,
                                   Date creationDate, String description, String notes, Double loanAmount,
                                   String rejectionNotes, Boolean canBeRefunded, Long relatedToId,
                                   Long beneficiaryId, String beneficiaryMobileNumber, String beneficiaryAccountName,
                                   String beneficiaryIban, String swift, Boolean isRefunded, String entityType) {
        this.id = id;
        this.token = token;
        this.amount = amount;
        this.amountInLocalCurrency = amountInLocalCurrency;
        this.paymentMethod = paymentMethod;
        this.status = status;
        this.creationDate = creationDate;
        this.description = description;
        this.notes = notes;
        this.loanAmount = loanAmount;
        this.rejectionNotes = rejectionNotes;
        this.canBeRefunded = canBeRefunded;
        this.relatedToId = relatedToId;
        this.beneficiaryId = beneficiaryId;
        this.beneficiaryMobileNumber = beneficiaryMobileNumber;
        this.beneficiaryAccountName = beneficiaryAccountName;
        this.beneficiaryIban = beneficiaryIban;
        this.swift = swift;
        this.isRefunded = isRefunded;
        this.entityType = entityType;
    }
    
    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getToken() { return token; }
    public void setToken(String token) { this.token = token; }
    
    public Double getAmount() { return amount; }
    public void setAmount(Double amount) { this.amount = amount; }
    
    public Double getAmountInLocalCurrency() { return amountInLocalCurrency; }
    public void setAmountInLocalCurrency(Double amountInLocalCurrency) { this.amountInLocalCurrency = amountInLocalCurrency; }
    
    public ExpensePaymentMethod getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) { this.paymentMethod = paymentMethod; }
    
    public ExpenseRequestStatus getStatus() { return status; }
    public void setStatus(ExpenseRequestStatus status) { this.status = status; }
    
    public Date getCreationDate() { return creationDate; }
    public void setCreationDate(Date creationDate) { this.creationDate = creationDate; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public Double getLoanAmount() { return loanAmount; }
    public void setLoanAmount(Double loanAmount) { this.loanAmount = loanAmount; }
    
    public String getRejectionNotes() { return rejectionNotes; }
    public void setRejectionNotes(String rejectionNotes) { this.rejectionNotes = rejectionNotes; }
    
    public Boolean getCanBeRefunded() { return canBeRefunded; }
    public void setCanBeRefunded(Boolean canBeRefunded) { this.canBeRefunded = canBeRefunded; }
    
    public Long getRelatedToId() { return relatedToId; }
    public void setRelatedToId(Long relatedToId) { this.relatedToId = relatedToId; }
    
    public Long getBeneficiaryId() { return beneficiaryId; }
    public void setBeneficiaryId(Long beneficiaryId) { this.beneficiaryId = beneficiaryId; }
    
    public String getBeneficiaryMobileNumber() { return beneficiaryMobileNumber; }
    public void setBeneficiaryMobileNumber(String beneficiaryMobileNumber) { this.beneficiaryMobileNumber = beneficiaryMobileNumber; }
    
    public String getBeneficiaryAccountName() { return beneficiaryAccountName; }
    public void setBeneficiaryAccountName(String beneficiaryAccountName) { this.beneficiaryAccountName = beneficiaryAccountName; }
    
    public String getBeneficiaryIban() { return beneficiaryIban; }
    public void setBeneficiaryIban(String beneficiaryIban) { this.beneficiaryIban = beneficiaryIban; }
    
    public String getSwift() { return swift; }
    public void setSwift(String swift) { this.swift = swift; }
    
    public Boolean getIsRefunded() { return isRefunded; }
    public void setIsRefunded(Boolean isRefunded) { this.isRefunded = isRefunded; }
    
    public String getEntityType() { return entityType; }
    public void setEntityType(String entityType) { this.entityType = entityType; }
    
    public Map<String, Object> getExpense() { return expense; }
    public void setExpense(Map<String, Object> expense) { this.expense = expense; }
    
    public Map<String, Object> getCurrency() { return currency; }
    public void setCurrency(Map<String, Object> currency) { this.currency = currency; }
    
    public Map<String, Object> getRequestedBy() { return requestedBy; }
    public void setRequestedBy(Map<String, Object> requestedBy) { this.requestedBy = requestedBy; }
    
    public Map<String, Object> getPurposeAdditionalDescription() { return purposeAdditionalDescription; }
    public void setPurposeAdditionalDescription(Map<String, Object> purposeAdditionalDescription) { this.purposeAdditionalDescription = purposeAdditionalDescription; }
    
    public Map<String, Object> getPayment() { return payment; }
    public void setPayment(Map<String, Object> payment) { this.payment = payment; }
    
    public Map<String, Object> getBucket() { return bucket; }
    public void setBucket(Map<String, Object> bucket) { this.bucket = bucket; }
}
