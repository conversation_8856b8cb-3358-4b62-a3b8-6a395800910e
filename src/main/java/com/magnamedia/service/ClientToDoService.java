package com.magnamedia.service;

/**
 * <AUTHOR>
 * @created 12/05/2024 - 1:57 PM
 * ACC-6385
 */

import com.magnamedia.core.Setup;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig.FlowEventName;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Stream;


@Service
public class ClientToDoService {
    private static final Logger logger = Logger.getLogger(ClientToDoService.class.getName());

    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    public enum ClientTodoFlow implements LabelValueEnum {
        ACCOUNTING_BOUNCED_PAYMENT_FLOW(DDMessagingType.BouncedPayment, "Accounting Bounced Payment Flow"),
        ACCOUNTING_REJECTED_SIGNATURE(DDMessagingType.DirectDebitRejected, "Accounting Rejected Signature"),
        ACCOUNTING_MISSING_DOCUMENTS(DDMessagingType.IncompleteDDRejectedByDataEntry, "Accounting Missing Documents"),
        ACCOUNTING_MISSING_SIGNATURE(DDMessagingType.IncompleteDDClientHasNoApprovedSignature, "Accounting Missing Signature") ,
        ACCOUNTING_UNPAID_CREDIT_CARD_PAYMENT(DDMessagingType.OnlineCreditCardPaymentReminders, "Accounting Unpaid Credit Card Payment"),
        ACCOUNTING_IPAM_PAYMENT_NEEDED(DDMessagingType.ClientPaidCashAndNoSignatureProvided, "Accounting IPAM Payment Needed");

        private final DDMessagingType event;
        private final String label;

        ClientTodoFlow(DDMessagingType event, String label) {
            this.event = event;
            this.label = label;
        }

        @Override
        public String getLabel() { return label; }

        public DDMessagingType getEvent() { return event; }

        public static ClientTodoFlow getFlowName(DDMessagingType type) {
           return Arrays.stream(ClientTodoFlow.values())
                   .filter(e -> e.getEvent().equals(type))
                   .findFirst()
                   .orElse(null);
        }
    }

    public void createClientTodo(DDMessagingContract d, FlowProcessorEntity entity, ContractPaymentTerm cpt, Map<String, String> map) {
        logger.info("contract Id: " + cpt.getContract().getId() + ", DDMessagingContract Id: " + d.getId());

        if (DDMessagingType.Termination.equals(d.getDdMessaging().getEvent()) &&
                Setup.getApplicationContext().getBean(ExtensionFlowService.class)
                .isEligibleForExtensionFlow(entity)) {
            logger.info("isEligibleForExtensionFlow");
            return;
        }

        Map<String, String> parameters = new HashMap<>();
        if (map.containsKey("bouncedPaymentId")) {
            parameters.put("bouncedPaymentId", map.get("bouncedPaymentId"));
        }

        flowProcessorMessagingService.fillParameters(
                d,
                entity,
                cpt, parameters, false);

        ClientTodoFlow flowName = ClientTodoFlow.getFlowName(
                DDMessagingType.valueOf(map.getOrDefault("triggeredFlow", d.getDdMessaging().getEvent().toString())));
        if (flowName == null) return;

        Map<String, Object> body = new HashMap<>();
        body.put("flowName", flowName.toString().toLowerCase());
        body.put("link", getLink(d, parameters));
        body.put("relatedEntityId", d.getId());
        body.put("relatedEntityType", d instanceof DDMessaging ? "DDMessaging" : "DDBankMessaging");
        if (entity != null){
            map.put("flowProcessorEntityId", entity.getId().toString());
            map.put("isOnlineReminderFlow", String.valueOf(entity.getFlowEventConfig()
                    .getName().equals(FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS)));
        }
        body.put("extraInfo", map);

        String Url = "/sales/clientenchantertodo/createautomatedtodo?" +
                "client=" + cpt.getContract().getClient().getId() +
                "&note=" + getNoteSection(d.getDdMessaging(), cpt.getContract(), entity, map) +
                "&type=" + "ACCOUNTING_TODO" +
                "&contract=" + cpt.getContract().getId() +
                "&toDoType=CLIENT";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("accounting_todo_extra_info", body);
        if(d.getDdMessaging().getEvent().equals(DDMessagingType.Termination)) {
            requestBody.put("ignoreContractCancellationBusinessRule", true);
        }

        logger.info("Sales API Url: " + Url + "; requestBody: " + requestBody);
        moduleConnector.postJsonAsync(Url, requestBody);
    }

    private String getNoteSection(DDMessaging d, Contract contract, FlowProcessorEntity entity, Map<String, String> parameters) {

        switch (d.getEvent()) {
            case BouncedPayment:
                return (contract.isMaidCc() ? "CC Contract" : "MV Contract") +
                        "- Client payment got bounced for the " + (Integer.parseInt(d.getTrials()) + 1) +
                            DateUtil.getOrdinalSuffix(Integer.parseInt(d.getTrials()) + 1) + " time";
            case OnlineCreditCardPaymentReminders:
                return "Online card payment flow, pending payment through link for " +
                        Days.daysBetween(new LocalDate(entity.getCreationDate()), LocalDate.now()).getDays() + " days";
            case DirectDebitRejected:
                return "Client’s signature got rejected for the " + Integer.parseInt(d.getTrials()) +
                            DateUtil.getOrdinalSuffix(Integer.parseInt(d.getTrials())) + " time";
            case IncompleteDDRejectedByDataEntry :
                return "DD rejected from data entry - Missing documents needed from client";
            case IncompleteDDClientHasNoApprovedSignature:
                return "Client’s adjusted end date is approaching and the client hasn’t signed the new DDS yet";
            case ClientPaidCashAndNoSignatureProvided:
                return "IPAM Flow- Client didn’t pay next month’s payment yet";
            case Termination:
                String reason = parameters.containsKey("contractCancellationReason") ?
                        PicklistHelper.getItem(
                                AccountingModule.PICKLIST_TERMINATION_REASON_LIST,
                                parameters.get("contractCancellationReason")).getName() :
                        "N/A";
                return (contract.isMaidCc() ? "CC" : "MV") + " Contract scheduled for termination, termination reason: " + reason;
        }
        return "";
    }

    private String getLink(DDMessagingContract d, Map<String, String> parameters) {

        switch (d.getDdMessaging().getEvent()) {
            case BouncedPayment:
                return Setup.getApplicationContext()
                        .getBean(ContractPaymentConfirmationToDoService.class)
                        .getPayingViaCreditCardLink(parameters.getOrDefault("todoUuid", ""));
            case OnlineCreditCardPaymentReminders:
                return parameters.getOrDefault("paytabs_link", "");
            case IncompleteDDRejectedByDataEntry:
            case IncompleteDDClientHasNoApprovedSignature:
            case DirectDebitRejected:
                return parameters.getOrDefault("link_send_dd_details", "");
            case ClientPaidCashAndNoSignatureProvided:
                return parameters.containsKey("paytabs_link") && !parameters.get("paytabs_link").isEmpty() ?
                        parameters.get("paytabs_link") :
                        parameters.getOrDefault("link_send_dd_details", "");
            case Termination:
                return parameters.getOrDefault("payment_link_termination_message", "");

        }
        return "";
    }

    public List<Map<String, String>> getMissingBankInfo(DDMessaging ddMessaging, Contract contract) {
        List<Map<String, String>> missingInfo = new ArrayList<>();
        String linkSendDdDetails = flowProcessorMessagingService.getLinkSendDdDetails(ddMessaging, contract, false);

        if (Stream.of("isEidRejected", "eid_rejection_bank_side", "eidIsMissing")
                .anyMatch(linkSendDdDetails::contains)) {
            missingInfo.add(new HashMap<String, String>() {{
                put("name", "EID");
                put("tag", "bank_info_eid");
            }});
        }

        if (Stream.of("isIbanRejected", "iban_rejection_bank_side", "ibanIsMissing")
                .anyMatch(linkSendDdDetails::contains)) {
            missingInfo.add(new HashMap<String, String>() {{
                put("name", "IBAN");
                put("tag", "bank_info_iban");
            }});
        }

        logger.info("missingInfo: " + missingInfo);
        return missingInfo;
    }

    // ACC-8650
    public void stopOldOnlineReminderFlowAndDeletedOldPayment(Map<String, Object> clientTodoInfo) {

        Map<String, Object> extraInfo = (Map<String, Object>)clientTodoInfo.get("extraInfo");
        if (!extraInfo.containsKey("isOnlineReminderFlow") ||
                !Boolean.parseBoolean(String.valueOf(extraInfo.get("isOnlineReminderFlow")))) { return; }

        FlowProcessorEntity f = flowProcessorEntityRepository.findOne(Long.parseLong(String.valueOf(extraInfo.get("flowProcessorEntityId"))));
        logger.info("Old online Reminder Flow id: " + f.getId());

        // 1- Stop Old Online Reminder Flow
        if (!f.isStopped() && !f.isCompleted()) {
            f.setStopped(true);
            flowProcessorEntityRepository.silentSave(f);
        }

        // 2- Stop Old Contract Payment Confirmation and old PRE_PDP Payment
        ContractPaymentConfirmationToDo t = f.getContractPaymentConfirmationToDo();
        if (f.getContractPaymentConfirmationToDo() == null) { return; }

        t.setDisabled(true);
        Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                .silentSave(t);
    }
}