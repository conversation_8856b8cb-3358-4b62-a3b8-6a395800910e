package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowEventConfig.FlowEventName;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig.FlowSubEventName;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.FlowEventConfigRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 16/04/2025 - 3:47 AM
 * ACC-8954
 */
@Service
public class ExtensionFlowService {
    private static final Logger logger = Logger.getLogger(ExtensionFlowService.class.getName());

    @Autowired
    private FlowEventConfigRepository flowEventConfigRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private AfterCashFlowService afterCashFlowService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;

    public void startExtensionFlow(ContractPaymentTerm cpt, FlowProcessorEntity flowCausedTermination) {

        try {
            logger.info("cpt id : " + cpt.getId() +
                    "; flow Caused Termination: " + flowCausedTermination);

            if (flowCausedTermination == null) return;

            ContractPaymentConfirmationToDo todo = flowCausedTermination.getContractPaymentConfirmationToDo();

            if (todo == null || todo.isDisabled()) {
                switch (flowCausedTermination.getFlowEventConfig().getName()) {
                    case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                        todo = afterCashFlowService.addConfirmationTodoForPayment(flowCausedTermination.getContract());
                        break;
                    case CLIENTS_PAYING_VIA_Credit_Card:
                        todo = clientPayingViaCreditCardService.createTodoIfNotExists(cpt, cpt.getContract().isOneMonthAgreement() ?
                                new LocalDate(cpt.getContract().getPaidEndDate()).plusDays(1) :
                                new LocalDate(cpt.getContract().getPaidEndDate()).plusMonths(1).dayOfMonth().withMinimumValue());
                        break;
                }
            }

            int startExtensionFlowDay = 1;
            FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(FlowEventName.EXTENSION_FLOW);
            if (flowEventConfig.hasTag("extension_flow_start_after_x_days")) {
                startExtensionFlowDay = Integer.parseInt(flowEventConfig.getTagValue("extension_flow_start_after_x_days").getValue());
            }

            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put("flowCausedTerminationId", flowCausedTermination.getId());

            // Start New Extension Flow
            Map<String, Object> map = new HashMap<>();
            map.put("trials", 0);
            map.put("reminders", 1);
            map.put("lastExecutionDate", new LocalDate(cpt.getContract().getPaidEndDate()).plusDays(startExtensionFlowDay).toDate());
            map.put("todo", todo);
            map.put("additionalInfo", additionalInfo);

            flowProcessorService.createFlowProcessor(FlowEventName.EXTENSION_FLOW, FlowSubEventName.PAYMENT_REMINDER_EXTENSION, cpt, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void handleStopExtensionFlow(FlowProcessorEntity flow) {
        FlowProcessorEntity flowCausedTermination = Setup.getRepository(FlowProcessorEntityRepository.class)
                .findOne(Utils.parseValue(flow.getAdditionalValue("flowCausedTerminationId"), Long.class));
        logger.info("flow id:" + flow + "; flow caused termination id: " + flowCausedTermination);

        // Reset the IPAM flow
        switch (flowCausedTermination.getFlowEventConfig().getName()) {
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                if (!afterCashFlowService.checkAndSwitchToPayingViaCC(flowCausedTermination)) {
                    afterCashFlowService.reactivateFlow(flowCausedTermination);
                }
                break;
        }
    }

    public static boolean erpAllowedExtensionFlow() {
        return Boolean.parseBoolean(
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_MV_EXTENSION_FLOW_ENABLED));
    }

    public boolean isEligibleForExtensionFlow(FlowProcessorEntity f) {

        if (f == null || !f.getContract().isActive() || !f.getContract().isMaidVisa()) return false;

        if (!f.getFlowEventConfig().getName().equals(FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED) &&
                (!f.getFlowEventConfig().getName().equals(FlowEventName.CLIENTS_PAYING_VIA_Credit_Card) ||
                        !f.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER))) return false;

        // Check if PED is not at end of current month
        if (!new DateTime(f.getContract().getPaidEndDate()).toString("yyyy-MM-dd")
                .equals(new LocalDate().dayOfMonth().withMaximumValue().toString("yyyy-MM-dd"))) return false;

        // Check if Extension Flow is enabled on ERP
        if (!ExtensionFlowService.erpAllowedExtensionFlow()) return false;

        // Check if Extension Flow is enabled for this contract
        if (ContractService.isExtensionFlowDisabled(f.getContract())) return false;

        // Check if IPAM or Monthly Reminder flow is not running
        return !flowProcessorService.existsRunningFlow(f.getContract(), FlowEventName.EXTENSION_FLOW);
    }
}