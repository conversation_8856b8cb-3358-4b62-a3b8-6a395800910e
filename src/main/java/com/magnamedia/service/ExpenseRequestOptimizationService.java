package com.magnamedia.service;

import com.magnamedia.core.entity.User;
import com.magnamedia.entity.dto.ApprovalExpenseRequestDto;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ExpenseRequestTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service to optimize ExpenseRequestTodo queries and avoid N+1 problems
 */
@Service
public class ExpenseRequestOptimizationService {

    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    /**
     * Get approval expense requests with optimized performance
     */
    public List<ApprovalExpenseRequestDto> getApprovalExpenseRequestsOptimized(User approveHolder) {
        // Get base data using optimized DTO query
        List<ApprovalExpenseRequestDto> dtos = expenseRequestTodoRepository
                .findApprovalRequestsOptimized(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString(), approveHolder);
        
        if (dtos.isEmpty()) {
            return dtos;
        }
        
        // Collect all IDs for batch loading
        Set<Long> expenseIds = new HashSet<>();
        Set<Long> currencyIds = new HashSet<>();
        Set<Long> requestedByIds = new HashSet<>();
        Set<Long> purposeIds = new HashSet<>();
        Set<Long> paymentIds = new HashSet<>();
        Set<Long> bucketIds = new HashSet<>();
        Set<Long> todoIds = dtos.stream().map(ApprovalExpenseRequestDto::getId).collect(Collectors.toSet());
        
        // Get related entity IDs from the original entities (minimal query)
        List<Object[]> relatedData = expenseRequestTodoRepository.findRelatedEntityIds(todoIds);
        
        for (Object[] row : relatedData) {
            Long expenseId = (Long) row[1];
            Long currencyId = (Long) row[2];
            Long requestedById = (Long) row[3];
            Long purposeId = (Long) row[4];
            Long paymentId = (Long) row[5];
            Long bucketId = (Long) row[6];
            
            if (expenseId != null) expenseIds.add(expenseId);
            if (currencyId != null) currencyIds.add(currencyId);
            if (requestedById != null) requestedByIds.add(requestedById);
            if (purposeId != null) purposeIds.add(purposeId);
            if (paymentId != null) paymentIds.add(paymentId);
            if (bucketId != null) bucketIds.add(bucketId);
        }
        
        // Batch load all related entities
        Map<Long, Map<String, Object>> expenseMap = loadExpensesAsMap(expenseIds);
        Map<Long, Map<String, Object>> currencyMap = loadPicklistItemsAsMap(currencyIds);
        Map<Long, Map<String, Object>> requestedByMap = loadUsersAsMap(requestedByIds);
        Map<Long, Map<String, Object>> purposeMap = loadPicklistItemsAsMap(purposeIds);
        Map<Long, Map<String, Object>> paymentMap = loadPaymentsAsMap(paymentIds);
        Map<Long, Map<String, Object>> bucketMap = loadBucketsAsMap(bucketIds);
        
        // Create lookup map for related data
        Map<Long, Object[]> relatedDataMap = relatedData.stream()
                .collect(Collectors.toMap(row -> (Long) row[0], row -> row));
        
        // Populate DTOs with related data
        for (ApprovalExpenseRequestDto dto : dtos) {
            Object[] related = relatedDataMap.get(dto.getId());
            if (related != null) {
                Long expenseId = (Long) related[1];
                Long currencyId = (Long) related[2];
                Long requestedById = (Long) related[3];
                Long purposeId = (Long) related[4];
                Long paymentId = (Long) related[5];
                Long bucketId = (Long) related[6];
                
                dto.setExpense(expenseMap.get(expenseId));
                dto.setCurrency(currencyMap.get(currencyId));
                dto.setRequestedBy(requestedByMap.get(requestedById));
                dto.setPurposeAdditionalDescription(purposeMap.get(purposeId));
                dto.setPayment(paymentMap.get(paymentId));
                dto.setBucket(bucketMap.get(bucketId));
            }
        }
        
        return dtos;
    }
    
    private Map<Long, Map<String, Object>> loadExpensesAsMap(Set<Long> ids) {
        if (ids.isEmpty()) return new HashMap<>();
        
        return expenseRepository.findByIdIn(ids).stream()
                .collect(Collectors.toMap(
                    expense -> expense.getId(),
                    expense -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", expense.getId());
                        map.put("name", expense.getName());
                        map.put("approvalMethod", expense.getApprovalMethod());
                        return map;
                    }
                ));
    }
    
    private Map<Long, Map<String, Object>> loadPicklistItemsAsMap(Set<Long> ids) {
        if (ids.isEmpty()) return new HashMap<>();
        
        return picklistItemRepository.findByIdIn(ids).stream()
                .collect(Collectors.toMap(
                    item -> item.getId(),
                    item -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", item.getId());
                        map.put("label", item.getLabel());
                        map.put("code", item.getCode());
                        return map;
                    }
                ));
    }
    
    private Map<Long, Map<String, Object>> loadUsersAsMap(Set<Long> ids) {
        if (ids.isEmpty()) return new HashMap<>();
        
        return userRepository.findByIdIn(ids).stream()
                .collect(Collectors.toMap(
                    user -> user.getId(),
                    user -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", user.getId());
                        map.put("name", user.getName());
                        return map;
                    }
                ));
    }
    
    private Map<Long, Map<String, Object>> loadPaymentsAsMap(Set<Long> ids) {
        if (ids.isEmpty()) return new HashMap<>();
        
        return expensePaymentRepository.findByIdIn(ids).stream()
                .collect(Collectors.toMap(
                    payment -> payment.getId(),
                    payment -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", payment.getId());
                        map.put("transaction", payment.getTransaction() != null ? payment.getTransaction().getId() : null);
                        return map;
                    }
                ));
    }
    
    private Map<Long, Map<String, Object>> loadBucketsAsMap(Set<Long> ids) {
        if (ids.isEmpty()) return new HashMap<>();
        
        return bucketRepository.findByIdIn(ids).stream()
                .collect(Collectors.toMap(
                    bucket -> bucket.getId(),
                    bucket -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", bucket.getId());
                        map.put("label", bucket.getLabel());
                        map.put("code", bucket.getCode());
                        return map;
                    }
                ));
    }
}
