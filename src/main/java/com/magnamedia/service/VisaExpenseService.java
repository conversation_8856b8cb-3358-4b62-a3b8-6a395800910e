package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.entity.*;
import com.magnamedia.extra.*;
import com.magnamedia.module.type.MaidContractType;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.visa.ExpensePurpose;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.magnamedia.extra.ExpenseStatus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
// ACC-5523
@Service
public class VisaExpenseService {

    private static final Logger logger = Logger.getLogger(VisaExpenseService.class.getName());

    @Autowired
    private NewVisaRequestExpenseRepository newVisaRequestExpenseRepository;
    @Autowired
    private RenewVisaRequestExpenseRepository renewVisaRequestExpenseRepository;
    @Autowired
    private CancelVisaRequestExpenseRepository cancelVisaRequestExpenseRepository;
    @Autowired
    private RepeatEIDExpenseRepository repeatEIDExpenseRepository;
    @Autowired
    private ContractModificationExpenseRepository contractModificationExpenseRepository;
    @Autowired
    private ModifyVisaRequestExpenseRepository modifyVisaRequestExpenseRepository;
    @Autowired
    private ModifyPersonInformationExpenseRepository modifyPersonInformationExpenseRepository;
    @Autowired
    private UnpaidLeaveExpenseRepository unpaidLeaveExpenseRepository;
    @Autowired
    private VisaExpenseConfigurationRepository visaExpenseConfigurationRepository;
    @Autowired
    private VisaExpensePaymentTypeDetailsRepository visaExpensePaymentTypeDetailsRepository;


    public List<VisaRequestExpense> searchById(
            SelectFilter filters) {

        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();
        try {
            Map<Integer, Object> parametrs = new HashMap<>();
            String queryStr = BuildSelectQuery(filters, parametrs, null, getSearchSelect());
            Query resultQuery = em.createNativeQuery(queryStr);
            for (Integer index : parametrs.keySet())
                resultQuery.setParameter(index, parametrs.get(index));

            return VisaExpenseBuildData(resultQuery.getResultList(), false);
        } finally {
            em.close();
        }
    }

    public AccountingPage getSearchData(
            SelectFilter filters, Pageable pageable) {

        if (filters == null || filters.isEmpty()) {
            filters = new SelectFilter("status", "=", ExpenseStatus.Pending);
        }

        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();

        try {
            Map<Integer, Object> parametrs = new HashMap<>();
            String queryStr = BuildSelectQuery(filters, parametrs, pageable, getSearchSelect());
            logger.log(Level.SEVERE, queryStr);
            Query resultQuery = em.createNativeQuery(queryStr);
            for (Integer index : parametrs.keySet())
                resultQuery.setParameter(index, parametrs.get(index));

            // ACC-2406
            List<VisaRequestExpense> visaRequestExpenses = VisaExpenseBuildData(resultQuery.getResultList(), false);

            String countQueryStr = buildCountQuery(filters, parametrs);
            Query countQuery = em.createNativeQuery(countQueryStr);
            for (Integer index : parametrs.keySet())
                countQuery.setParameter(index, parametrs.get(index));

            //Jiira ACC-2406
            Long countQueryValue = ((BigInteger) countQuery.getSingleResult()).longValue();

            String sumQueryStr = BuildSumQuery(filters, parametrs);
            Query sumQuery = em.createNativeQuery(sumQueryStr);
            for (Integer index : parametrs.keySet())
                sumQuery.setParameter(index, parametrs.get(index));

            // ACC-2406
            Double sumQueryValue = (Double) sumQuery.getSingleResult();

            return new AccountingPage(visaRequestExpenses,
                            pageable, countQueryValue,
                            sumQueryValue != null ?
                                    Double.parseDouble(new DecimalFormat("############.##").format((double) sumQueryValue)) : 0D);
        } finally {
            em.close();
        }
    }

    public List<VisaRequestExpenseCSV> getCsvData(
            SelectFilter filters) {

        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();
        try {
            Map<Integer, Object> parameters = new HashMap<>();
            String queryStr = BuildSelectQuery(filters, parameters, null, getSearchSelect());
            Query resultQuery = em.createNativeQuery(queryStr);
            for (Integer index : parameters.keySet())
                resultQuery.setParameter(index, parameters.get(index));

            return VisaExpenseBuildData(resultQuery.getResultList(), true);
        } finally {
            em.close();
        }
    }

    private List<Object> VisaExpenseBuildData(
            List<Object[]> visaExpenses, boolean forCsv) {

        List<Object> l = new ArrayList<>();
        Map<String, Map<String, Object>> defaultFromBuckets = getDefaultFromBuckets();

        visaExpenses.forEach(v -> {

            String contractProspectType = v[3] != null ? (String) v[3] : v[4] != null ? (String) v[4] : "";

            boolean isMaidVisa = false;
            if (contractProspectType.equals("maids.cc/VisaServices")){
                contractProspectType = "Maid.Visa";
                isMaidVisa = true;
            }

            Long transactionId = v[9] == null ? null : ((BigInteger) v[19]).longValue();
            String description = (String) v[10];
            String fromBucketName = "";
            String expenseName = "";
            Long fromBucketId = null;
            String fromBucketCode = "";
            Long expenseId = null;
            ExpensePurpose expensePurpose = v[15] != null ? ExpensePurpose.valueOf((String) v[15]) : null;

            EmployeeType employeeType = null;
            String housemaidName = (String) v[5];
            String officeStaffName = (String) v[6];
            PaymentType paymentType = v[7] == null ? null : PaymentType.valueOf((String) v[7]);
            boolean newEmployee = Boolean.parseBoolean((String) v[8]);

            if (housemaidName != null) {
                employeeType = EmployeeType.Housemaid;
            } else if (officeStaffName != null) {
                employeeType = EmployeeType.Officestaff;
            }
            if (transactionId != null) {
                // get data from the transaction
                logger.log(Level.INFO, "transactionId: {0}", transactionId);
                fromBucketCode = v[11] == null ? "" : (String) v[11];
                expenseName = v[12] == null ? "" : (String) v[12];
                fromBucketId = v[19] == null ? null : ((BigInteger) v[19]).longValue();
                fromBucketName = v[24] == null ? "" : (String) v[24];
                expenseId = v[20] == null ? null : ((BigInteger) v[20]).longValue();

            } else {
                if (paymentType != null &&
                        employeeType != null &&
                        expensePurpose != null) {

                    // ACC-7372
                    if (PaymentType.Credit_Card.equals(paymentType) && v[25] != null) {
                        String bucketCode = (String) v[25];
                        logger.info("bucket code: " + bucketCode);
                        List<Object[]> buckets = Setup.getRepository(BucketRepository.class)
                                .findIdAndNameByCode(Collections.singletonList(bucketCode));
                        if (!buckets.isEmpty()) {
                            fromBucketCode = (String) buckets.get(0)[0];
                            fromBucketId = (Long) buckets.get(0)[1];
                            fromBucketName = (String) buckets.get(0)[2];

                        }

                    } else if (defaultFromBuckets.containsKey(paymentType.toString().toLowerCase()) &&
                            (employeeType.equals(EmployeeType.Housemaid) ||
                                    (employeeType.equals(EmployeeType.Officestaff)))) {
                        fromBucketName = (String) defaultFromBuckets.get(paymentType.toString().toLowerCase()).get("name");
                        fromBucketId = (Long) defaultFromBuckets.get(paymentType.toString().toLowerCase()).get("id");
                        fromBucketCode = (String) defaultFromBuckets.get(paymentType.toString().toLowerCase()).get("code");
                    }

                    Object[] expense = getDefaultExpense(
                            expensePurpose, employeeType, newEmployee, isMaidVisa, paymentType);
                    if (expense != null) {
                        expenseName = (String) expense[2];
                        expenseId = (Long) expense[1];
                    }
                }

                description = (housemaidName != null ? housemaidName :
                        officeStaffName != null ? officeStaffName : "") +
                        (employeeType != null && employeeType.equals(EmployeeType.Housemaid) ? " / Maid's Profile ID: " + ((BigInteger) v[17]).longValue() : "" ) +
                        " / " + (newEmployee ? "New " : "Renewal ") +
                        (isMaidVisa ? "MV " : "CC ") +
                        (employeeType == null ? "" : (employeeType.equals(EmployeeType.Housemaid) ? "maid" : employeeType) + " / ") +
                        (expensePurpose != null ? expensePurpose.getLabel() : "");

                // ACC-6912 @Existing Description Format@ / @Creation Date@ / @Reference Number@
                description += " / " + new LocalDate(v[2]).toString("yyyy-MM-dd") + (v[14] == null ? "" : (" / "  + v[14]));
            }

            Object row;
            if (forCsv) {
                row = new VisaRequestExpenseCSV(
                        ((BigInteger) v[0]).longValue(),
                        (String) v[1],
                        (Date) v[2],
                        housemaidName,
                        description,
                        fromBucketCode,
                        expenseName,
                        paymentType,
                        (Double) v[13],
                        (String) v[14],
                        employeeType,
                        contractProspectType);
            } else {
                Map<String, Object> housemaid = null;
                Map<String, Object> officeStaff = null;
                Map<String, Object> fromBucket = null;
                Map<String, Object> expense = null;
                Map<String, Object> transaction = null;
                Double amount = (Double) v[21];
                Double charge = (Double) v[22];
                Double vatCharge = (Double) v[23];

                if (employeeType != null && employeeType.equals(EmployeeType.Housemaid)) {
                    housemaid = new HashMap<String, Object>() {{
                        put("id", ((BigInteger) v[17]).longValue());
                        put("label", housemaidName);
                    }};

                } else if (employeeType != null && employeeType.equals(EmployeeType.Officestaff)) {
                    officeStaff = new HashMap<String, Object>() {{
                        put("id", ((BigInteger) v[18]).longValue());
                        put("label", officeStaffName);
                    }};
                }

                if (fromBucketId != null) {
                    logger.log(Level.INFO, "fromBucketId: {0}", fromBucketId);
                    fromBucket = new HashMap<>();
                    fromBucket.put("id", fromBucketId);
                    fromBucket.put("name", fromBucketName);
                }

                if (expenseId != null) {
                    logger.log(Level.INFO, "expenseId: {0}", expenseId);
                    expense = new HashMap<>();
                    expense.put("id", expenseId);
                    expense.put("nameLabel", expenseName);
                }

                if (transactionId != null) {
                    transaction = new HashMap<String, Object>() {{
                        put("id", transactionId);
                    }};
                }

                row = new VisaRequestExpense(
                        ((BigInteger) v[0]).longValue(),
                        (String) v[1],
                        (Date) v[2],
                        housemaid,
                        description,
                        fromBucket,
                        expense,
                        paymentType,
                        (Double) v[13],
                        (String) v[14],
                        employeeType,
                        contractProspectType,
                        isMaidVisa,
                        amount + " (amount) + " +
                                (charge != null ? charge : 0) + " (charge) + " +
                                (vatCharge != null ? new DecimalFormat("############.##").format(vatCharge) : 0) + " ( vat charge ) ",
                        transaction,
                        (String) v[16],
                        officeStaff);
            }

            l.add(row);
        });

        return l;
    }

    public Map<String, Map<String, Object>> getDefaultFromBuckets() {

        Map<String, Map<String, Object>> defaultFromBucket = new HashMap<>();

        Map<String, String> map = new HashMap<String, String>() {{
            put(Setup.getParameter(Setup.getCurrentModule(),
                    "edirhams_visa_expense_transaction_from_bucket"), "edirhams");
            put(Setup.getParameter(Setup.getCurrentModule(),
                    "noqoodi_visa_expense_transaction_from_bucket"), "noqoodi");
            put(Setup.getParameter(Setup.getCurrentModule(),
                    "cbd_visa_expense_transaction_from_bucket"), "cbd");
            put(Setup.getParameter(Setup.getCurrentModule(), // VPM-4439
                    "ewallet_visa_expense_transaction_from_bucket"), "ewallet");
            put(Setup.getParameter(Setup.getCurrentModule(),
                    "cash_visa_expense_transaction_from_bucket"), "cash");
            put(Setup.getParameter(Setup.getCurrentModule(), // VPM-4550
                    "pay_pro_wallet_visa_expense_transaction_from_bucket"), "paypro_wallet");
        }};

        Setup.getRepository(BucketRepository.class)
                .findIdAndNameByCode(new ArrayList<>(map.keySet())).forEach(o -> {
                    Map<String, Object> m = new HashMap<String, Object>() {{
                        put("code", o[0]);
                        put("id", o[1]);
                        put("name", o[2]);
                    }};
                    defaultFromBucket.put(map.get((String) o[0]), m);
                });

        return defaultFromBucket;
    }

    public Object[] getDefaultExpense(
            ExpensePurpose expensePurpose, EmployeeType employeeType,
            boolean newEmployee, boolean isMaidVisa, PaymentType paymentType) {

        if (employeeType == null ||
                (!employeeType.equals(EmployeeType.Housemaid) &&
                        !employeeType.equals(EmployeeType.Officestaff)))
            return null;

        VisaExpenseConfiguration.EmployeeType visaEmployeeType = employeeType.equals(EmployeeType.Officestaff) ?
                VisaExpenseConfiguration.EmployeeType.OFFICE_STAFF :
                isMaidVisa ? VisaExpenseConfiguration.EmployeeType.MAID_VISA :
                        VisaExpenseConfiguration.EmployeeType.MAID_CC;

        List<Object[]>  l = visaExpenseConfigurationRepository.findExpenseIdAndName(visaEmployeeType, newEmployee, expensePurpose, paymentType);

        return l.isEmpty() ? null : l.get(0);
    }

    private String BuildSelectQuery(
            SelectFilter filters,
            Map<Integer, Object> parameters,
            Pageable pageable,
            String selectStatement) {

        String result = "SELECT * FROM (";
        result += BuildUnionQuery(filters, parameters, selectStatement, true);
        result += ") as t ";
        if (pageable != null) {
            result += "limit " + pageable.getOffset() + ", " + pageable.getPageSize();
        }
        return result;
    }

    private String BuildSumQuery(
            SelectFilter filters,
            Map<Integer, Object> parameters) {

        String result = "SELECT SUM(AMOUNT) FROM (";
        result += BuildUnionQuery(filters, parameters, getSumSelect(), false);
        result += ") as t";
        return result;
    }

    private String buildCountQuery(
            SelectFilter filters,
            Map<Integer, Object> parameters) {

        String result = "SELECT COUNT(t.visaRequestExpenseID) FROM (";
        result += BuildUnionQuery(filters, parameters, getCountSelect(), false);
        result += ") as t";
        return result;
    }

    private String BuildUnionQuery(
            SelectFilter filters,
            Map<Integer, Object> parameters,
            String selectStatement,
            boolean withAdditionalJoin) {

        String conditions = getConditions(filters, parameters);
        //new
        String result = selectStatement;
        result += getFrom("NEWREQUESTEXPENSES", "NEWREQUESTS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //renew
        result += selectStatement;
        result += getFrom("RENEWREQUESTEXPENSES", "RENEWREQUESTS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //cancel
        result += selectStatement;
        result += getFrom("CANCELREQUESTEXPENSES", "CANCELREQUESTS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //repeateid
        result += selectStatement;
        result += getFrom("REPEATEIDREQUESTEXPENSES", "REPEATEIDREQUESTS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //Contract modification
        result += selectStatement;
        result += getFrom("CONTRACTMODIFICATIONEXPENSES", "CONTRACTMODIFICATIONS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //Modify visa
        result += selectStatement;
        result += getFrom("MODIFYVISAREQUESTEXPENSES", "MODIFYVISAREQUESTS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //Unpaid leave
        result += selectStatement;
        result += getFrom("UNPAIDLEAVEEXPENSES", "UNPAIDLEAVEREQUESTS", withAdditionalJoin);
        result += conditions;
        result += " UNION ";
        //Modify person information
        result += selectStatement;
        result += getFrom("MODIFYPERSONINFORMATIONEXPENSES", "MODIFYPERSONINFORMATIONREQUESTS", withAdditionalJoin);
        result += conditions;
        return result;
    }

    private String getCountSelect() {
        return " SELECT ne.ID as visaRequestExpenseID ";
    }

    private String getSearchSelect() {
        return "SELECT ne.ID as visaRequestExpenseID, " +
                "ne.ENTITY_TYPE visaExpenseType, " +
                "ne.CREATION_DATE, " +
                "pi.NAME as cType, " +
                "(SELECT pi1.NAME from CONTRACTS c1 " +
                "inner join PICKLISTS_ITEMS pi1 on c1.CONTRACT_PROSPECT_TYPE_ID = pi1.ID " +
                "where c1.HOUSEMAID_ID = h.ID order by c1.CREATION_DATE desc limit 1 ), " +
                "h.NAME as hName, " +
                "o.NAME as oName, " +
                "ne.PAYMENT_TYPE, " +
                "IF(nr.NEW_EMPLOYEE = true, 'true', 'false'), " +
                "ne.TRANSACTION_ID, " +
                "t.DESCRIPTION, " +
                "b.CODE, " +
                "CASE " +
                "when e.deleted = true THEN CONCAT(e.name, ' (Deleted)')  " +
                "when e.disabled = true then CONCAT(e.name, ' (Disabled)') " +
                "ELSE e.name END as eName, " +
                "(IFNULL(ne.AMOUNT, 0) + IFNULL(ne.CHARGE, 0) + IFNULL(ne.VAT_CHARGE, 0)), " +
                "ne.REFERENCE_NUMBER, " +
                "ne.PURPOSE, " +
                "ne.STATUS, " +
                "h.ID as hId, " +
                "o.ID as oId, " +
                "b.ID as bId, " +
                "e.ID as eId, " +
                "ne.AMOUNT, " +
                "ne.CHARGE, " +
                "ne.VAT_CHARGE, " +
                "b.NAME as bName, " +
                "ne.DEFAULT_BUCKET_CODE ";
    }

    private String getSumSelect() {
        return "SELECT ne.ID, ne.AMOUNT ";
    }

    private String getFrom(
            String entityClass,
            String entityClass2,
            boolean withAdditionalJoin) {

        String s = "FROM " + entityClass.toUpperCase() + " ne " +
                "left join " + entityClass2.toUpperCase() + " nr on ne.REQUEST_ID = nr.ID " +
                "left join HOUSEMAIDS h on nr.HOUSEMAID_ID = h.ID " +
                "left join OFFICESTAFFS o on nr.OFFICE_STAFF_ID = o.ID " +
                "left join CONTRACTS c on c.HOUSEMAID_ID = h.id and c.STATUS = 'ACTIVE' " +
                "   and c.ID = " +
                        "(select c1.ID  " +
                        "from CONTRACTS c1 " +
                        "where c1.HOUSEMAID_ID = h.ID and c1.STATUS = 'ACTIVE' " +
                        "order by c1.CREATION_DATE desc " +
                        "limit 1)  " +
                "left join PICKLISTS_ITEMS pi on c.CONTRACT_PROSPECT_TYPE_ID = pi.ID ";

        if (withAdditionalJoin)
            s += "left join TRANSACTIONS t on t.ID = ne.TRANSACTION_ID " +
                    "left join BUCKETS b on b.ID = t.FROM_BUCKET_ID " +
                    "left join EXPENSES e on e.ID = t.EXPENSE_ID ";
        return s;
    }

    private String getConditions(
            SelectFilter filters, Map<Integer, Object> parametrs) {

        StringBuilder sb = new StringBuilder();
        if (filters != null && !filters.isEmpty()) {
            sb.append(" WHERE ");
            renderFilter(filters, sb, parametrs, 1);
        }
        return sb.toString();
    }

    private int renderFilter(
            SelectFilter fb,
            StringBuilder sb,
            Map<Integer, Object> parametersMap,
            int parameterIndex) {

        if (fb.getField() != null && fb.getOperation() != null) {
            parameterIndex = renderSqlFilter(
                    fb.getField(), fb.getOperation(), fb.getValue(),
                    sb, parametersMap, parameterIndex);
            return parameterIndex;
        } else {
            sb.append(" ( ");
            parameterIndex = renderFilter(
                    fb.getLeft(), sb, parametersMap, parameterIndex);
            if (fb.isAnd())
                sb.append(" and ");
            else
                sb.append(" or ");
            parameterIndex = renderFilter(
                    fb.getRight(), sb, parametersMap, parameterIndex);
            sb.append(" ) ");
            return parameterIndex;
        }
    }


    private int renderSqlFilter(
            String field,
            String operation,
            Object objectValue,
            StringBuilder sb,
            Map<Integer, Object> parametersMap,
            int parameterIndex) {

        switch (field) {
            case "id":
                sb.append("ne.ID");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue);
                }
                parameterIndex++;
                break;
            case "creationDate":
                sb.append("ne.CREATION_DATE");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue);
                }
                parameterIndex++;
                break;
            case "employeeType":
                if (((EmployeeType) objectValue).equals(EmployeeType.Housemaid)) {
                    sb.append("nr.HOUSEMAID_ID");
                    sb.append(" ").append("IS NOT NULL");
                } else if (((EmployeeType) objectValue).equals(EmployeeType.Officestaff)) {
                    sb.append("nr.OFFICE_STAFF_ID");
                    sb.append(" ").append("IS NOT NULL");
                }
                break;
            case "maidContractType":
                if (((MaidContractType) objectValue).equals(MaidContractType.Maid_Visa)) {
                    sb.append("pi.CODE");
                    sb.append(" ").append("LIKE");
                    sb.append(" ").append("'maidvisa.ae_prospect'");
                } else {
                    sb.append(" ( ");
                    sb.append("pi.ID");
                    sb.append(" ").append("IS NULL");
                    sb.append(" or ");
                    sb.append("pi.CODE");
                    sb.append(" ").append("NOT LIKE");
                    sb.append(" ").append("'maidvisa.ae_prospect'");
                    sb.append(" ) ");
                }

                break;
            case "employeeName":
                sb.append(" ( ");
                sb.append("h.NAME");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue);
                    parameterIndex++;
                }
                sb.append(" or ");
                sb.append("o.NAME");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue);
                    parameterIndex++;
                }
                sb.append(" ) ");
                break;
            case "paymentType":
                sb.append("ne.PAYMENT_TYPE");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue.toString());
                }
                parameterIndex++;
                break;
            case "amount":
                sb.append("ne.AMOUNT");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue);
                }
                parameterIndex++;
                break;
            case "referenceNumber":
                sb.append("ne.REFERENCE_NUMBER");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue);
                }
                parameterIndex++;
                break;
            case "status":
                sb.append("ne.STATUS");
                sb.append(" ").append(operation);
                if (objectValue != null) {
                    sb.append(" ?").append(parameterIndex);
                    parametersMap.put(parameterIndex, objectValue.toString());
                }
                parameterIndex++;
                break;
        }
        return parameterIndex;
    }

    public List<VisaExpense> findVisaExpensesById(Long searchId){
        List<VisaExpense> VisaExpenses = new ArrayList<>();

        CancelRequestExpense cancelRequestExpense = cancelVisaRequestExpenseRepository.findOne(searchId);
        if (cancelRequestExpense != null) VisaExpenses.add(cancelRequestExpense);

        NewRequestExpense newRequestExpense = newVisaRequestExpenseRepository.findOne(searchId);
        if (newRequestExpense != null) VisaExpenses.add(newRequestExpense);

        RenewRequestExpense renewRequestExpense = renewVisaRequestExpenseRepository.findOne(searchId);
        if (renewRequestExpense != null) VisaExpenses.add(renewRequestExpense);

        RepeatEIDRequestExpense repeatEIDRequestExpense = repeatEIDExpenseRepository.findOne(searchId);
        if (repeatEIDRequestExpense != null) VisaExpenses.add(repeatEIDRequestExpense);

        ContractModificationExpense contractModificationExpense = contractModificationExpenseRepository.findOne(searchId);
        if (contractModificationExpense != null) VisaExpenses.add(contractModificationExpense);

        ModifyVisaRequestExpense modifyVisaRequestExpense = modifyVisaRequestExpenseRepository.findOne(searchId);
        if (modifyVisaRequestExpense != null) VisaExpenses.add(modifyVisaRequestExpense);

        return VisaExpenses;
    }

    public void saveVisaExpenseByType(
            String visaRequestExpenseType, VisaExpense expense){

        logger.log(Level.SEVERE, "saveVisaExpenseByType visaRequestExpenseType = " + visaRequestExpenseType
                + " visaRequestExpenseID = " + expense.getId());

        switch (visaRequestExpenseType.toLowerCase()) {
            case "newrequestexpense":
                newVisaRequestExpenseRepository.save((NewRequestExpense) expense);
                break;
            case "renewrequestexpense":
                renewVisaRequestExpenseRepository.save((RenewRequestExpense) expense);
                break;
            case "cancelrequestexpense":
                cancelVisaRequestExpenseRepository.save((CancelRequestExpense) expense);
                break;
            case "repeateidrequestexpense":
                repeatEIDExpenseRepository.save((RepeatEIDRequestExpense) expense);
                break;
            case "contractmodificationexpense":
                contractModificationExpenseRepository.save((ContractModificationExpense) expense);
                break;
            case "modifyvisarequestexpense":
                modifyVisaRequestExpenseRepository.save((ModifyVisaRequestExpense) expense);
                break;
            case "unpaidleaveexpense":
                unpaidLeaveExpenseRepository.save((UnpaidLeaveExpense) expense);
                break;
            case "modifypersoninformationexpense":
                modifyPersonInformationExpenseRepository.save((ModifyPersonInformationExpense) expense);
                break;
        }
    }

    public VisaExpense getVisaExpenseByType(
            String visaRequestExpenseType, Long visaRequestExpenseID){

        logger.log(Level.SEVERE, "getVisaExpenseByType visaRequestExpenseType = " + visaRequestExpenseType
                + " visaRequestExpenseID = " + visaRequestExpenseID);

        switch (visaRequestExpenseType.toLowerCase()) {
            case "newrequestexpense":
                return newVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "renewrequestexpense":
                return renewVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "cancelrequestexpense":
                return cancelVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "repeateidrequestexpense":
                return repeatEIDExpenseRepository.findOne(visaRequestExpenseID);
            case "contractmodificationexpense":
                return contractModificationExpenseRepository.findOne(visaRequestExpenseID);
            case "modifyvisarequestexpense":
                return modifyVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "unpaidleaveexpense":
                return unpaidLeaveExpenseRepository.findOne(visaRequestExpenseID);
            case "modifypersoninformationexpense":
                return modifyPersonInformationExpenseRepository.findOne(visaRequestExpenseID);
            default:
                return null;
        }
    }

    public String dismissVisaRequest(String visaRequestExpenseType, Long visaRequestExpenseID) {
        if (visaRequestExpenseType == null || visaRequestExpenseID == null) return null;

        VisaExpense expense = getVisaExpenseByType(visaRequestExpenseType, visaRequestExpenseID);
        if (expense == null) return null;

        expense.setStatus(ExpenseStatus.Dismissed);
        saveVisaExpenseByType(visaRequestExpenseType, expense);

        return expense.getName();
    }


    // VPM-4858 ACC-6912
    public void updateVisaExpenseCharge(String visaRequestExpenseType, Long visaRequestExpenseID) {
        VisaExpense expense = getVisaExpenseByType(visaRequestExpenseType, visaRequestExpenseID);
        logger.info("expense id: " + visaRequestExpenseID + "; type: " + visaRequestExpenseType);

        if (expense == null) return;

        expense.setCharge(0.0);
        expense.setVatCharge(0.0);
        if (expense.getPaymentType() != null && expense.getPurpose() != null &&
                !expense.getPurpose().equals(ExpensePurpose.REFUND_FOR_ENTRY_VISA)) {
            VisaExpensePaymentTypeDetails v = visaExpensePaymentTypeDetailsRepository
                    .findFirstByPaymentTypeAndExpensePurpose(expense.getPaymentType(), expense.getPurpose());

            if (v != null) {
                logger.info("VisaExpensePaymentTypeDetails id: " + v.getId());
                expense.setCharge(v.getCharge());
                expense.setVatCharge(v.getVatCharge());
            }
        }

        saveVisaExpenseByType(visaRequestExpenseType, expense);
    }

    public static Double calculateVatViaVisaExpensePaymentTypeDetails(VisaExpensePaymentTypeDetails v) {
        if (v == null) return 0.0;
        // ACC-6912 #3 Update VAT amount cal]culation
            /*
                VAT Amount = VAT amount for the Expense + VAT amount for the Payment Where
                VAT Amount for the Expense = Expense type charge * VAT charge of expense And
                VAT Amount for the payment = Payment type charged based on expense type * VAT charge of payment
            */
        BigDecimal vatOfPayment = BigDecimal.valueOf((v.getCharge() != null ? v.getCharge() : 0.0) *
                ((v.getVatChargePercentage() != null ? v.getVatChargePercentage() : 0.0) / 100));
        BigDecimal vatOfExpense = BigDecimal.valueOf(v.getServiceChargeOfExpense() * (v.getVatChargeOfExpense() / 100));

        return vatOfPayment.add(vatOfExpense).doubleValue();
    }
}