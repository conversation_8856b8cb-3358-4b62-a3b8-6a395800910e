package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.configuration.ObjectMapperConfiguration;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.extra.*;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.payment.PaymentDeletionRules;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
/**
 *
 * <AUTHOR> Sheikh Albalad
 * Created on Feb 16, 2022
 */
@Service
public class PaymentService {
    protected static final Logger logger = Logger.getLogger(PaymentService.class.getName());

    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractPaymentWrapperRepository contractPaymentWrapperRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundToDoRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ClientMessagingAndRefundService clientMessagingAndRefundService;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    TransactionRepository transactionRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private HousemaidService housemaidService;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;

    // Moved from CM ACC-6103
    public Payment createPayment(Payment payment, boolean overridePermissions, boolean silentSave) {
        allowedMethodsWithPaymentCheck(payment);

        if (canUpdatePayment(null, payment, payment.getStatus()) || overridePermissions) {
            payment = silentSave ? paymentRepository.silentSave(payment) : paymentRepository.saveAndFlush(payment);

            matchWireTransfer(payment);

            if (payment.getTypeOfPayment().toString().equals("Discount")) sendNotification(payment);

            payment.setTypeOfPayment(Setup.getRepository(PicklistItemRepository.class).findOne(payment.getTypeOfPayment().getId()));
            return payment;
        }

        return null;
    }

    public boolean canUpdatePayment(
            Payment oldPayment,
            Payment payment,
            PaymentStatus newStatus) {

        if (oldPayment != null && (oldPayment.getStatus() == null ||
                oldPayment.getStatus().equals(PaymentStatus.DELETED))) return false;

        User loggedUser = CurrentRequest.getUser();
        if (loggedUser == null) return true;

        String lockOverride = "payment_lock_override";
        String changeStatusPosition = "change_payment_status";
        String changePositionBounced = "change_payment_status_bounced";
        String changeChequeStatus = "change_cheque_status";

        boolean updateCheque = oldPayment != null && loggedUser.hasPosition(changeChequeStatus)
                && newStatus != null && newStatus.equals(PaymentStatus.TEARED_UP) &&
                !oldPayment.getStatus().equals(PaymentStatus.RECEIVED);
        if (updateCheque) {
            logger.info("updateCheque");
            return true;
        }

        if (loggedUser.hasPosition(lockOverride) || !payment.isLocked()) {
            boolean noReceivedPermission = (newStatus.equals(PaymentStatus.RECEIVED) ||
                    (oldPayment != null && oldPayment.getStatus().equals(PaymentStatus.RECEIVED))) &&
                    !loggedUser.hasPosition(changeStatusPosition);

            if(noReceivedPermission) logger.info("noReceivedPermission");

            boolean bouncedPermission = oldPayment == null || !oldPayment.getStatus().equals(PaymentStatus.BOUNCED) ||
                    loggedUser.hasPosition(changePositionBounced);
            if(bouncedPermission) logger.info("bouncedPermission");
            return bouncedPermission;
        }

        return false;
    }

    // ACC-789
    public void matchWireTransfer(Payment p) {
        if (!(p.getBusinessObjectType() != null
                && !p.getBusinessObjectType().isEmpty()
                && p.getBusinessObjectType().equals("ExpectedWireTransfer,Transaction")
                && p.getBusinessObjectId() != null
                && !p.getBusinessObjectId().isEmpty())) return;

        String[] businessObjectIds = p.getBusinessObjectId().split(",");
        if (businessObjectIds.length == 2) {
            Long expectedWireTransferId = Long.parseLong(businessObjectIds[0]);
            Long transactionId = Long.parseLong(businessObjectIds[1]);

            TransactionRepository transactionRepository =
                    Setup.getRepository(TransactionRepository.class);
            ExpectedWireTransferRepository expectedWireTransferRepository =
                    Setup.getRepository(ExpectedWireTransferRepository.class);

            Transaction transaction = transactionRepository.findOne(transactionId);
            ExpectedWireTransfer expectedWireTransfer =
                    expectedWireTransferRepository.findOne(expectedWireTransferId);

            if (expectedWireTransfer != null
                    && transaction != null
                    && !transaction.isMatchedWithExpectedWireTransfer()) {

                ClientTransactionRepository clientTransactionRepository = Setup.getRepository(ClientTransactionRepository.class);
                ClientTransaction  clientTransaction = new ClientTransaction(transaction , expectedWireTransfer.getClient());
                clientTransaction = clientTransactionRepository.saveAndFlush(clientTransaction);

                Revenue revenue = transaction.getRevenue();
                if (revenue != null && revenue.getCode().startsWith("UWR")) {
                    String code = null;
                    if (p.getContract() != null && p.getContract().getContractProspectType() != null)
                        code = p.getContract().getContractProspectType().getCode();

                    if (code != null && code.equals(PicklistItem.getCode("maids.cc prospect"))) {
                        revenue = Setup.getRepository(RevenueRepository.class).findByCode("FTR 02");
                        transaction.setRevenue(revenue);
                    } else if (code != null && code.equals(PicklistItem.getCode("maidvisa.ae prospect"))) {
                        revenue = Setup.getRepository(RevenueRepository.class).findByCode("MVR 02");
                        transaction.setRevenue(revenue);
                    }
                }

                if (p.getVat() != null && p.getVat()  != 0) {
                    transaction.setVatAmount(p.getVat());
                    transaction.setVatType(VatType.OUT);
                }
                if (p.getContract() != null && p.getContract().getClient() != null) {
                    transaction.setDescription(transaction.getDescription() + " - Client: " +
                            p.getContract().getClient().getName());
                }

                for (Attachment att : p.getAttachments()) {
                    if (att == null || att.getTag() == null) continue;

                    if (att.getTag().startsWith(Payment.FILE_TAG_PAYMENTS_TAX_INVOICE)) {
                        Attachment copied = Storage.cloneTemporary(att, "VAT");
                        if (transaction.getAttachments() == null)
                            transaction.setAttachments(new ArrayList<>());
                        transaction.addAttachment(copied);
                        break;
                    }
                }

                transaction.setTransactionType(TransactionEntityType.CLIENT);
                List<ClientTransaction> clientTransactions = transaction.getClients();
                if(clientTransactions==null) {
                    clientTransactions = new ArrayList<ClientTransaction>();
                }
                clientTransactions.add(clientTransaction);

                transaction.setClients(clientTransactions);
                transactionRepository.save(transaction);

                expectedWireTransfer.setPaymentId(p.getId());
                expectedWireTransfer.setTransaction(transaction);
                expectedWireTransfer.setStatus(ExpectedWireTransferStatus.Matched);
                expectedWireTransferRepository.save(expectedWireTransfer);
            }
        }

        if (p.getReplacementFor() != null) {
            p.setIsReplacement(true);

            Payment oldPayment = paymentRepository.findOne(p.getReplacementFor().getId());
            oldPayment.setReplaced(true);
            Setup.getApplicationContext().getBean(PaymentService.class).forceUpdatePayment(oldPayment);

            p.setWorkerSalary(oldPayment.getWorkerSalary());
        }
    }

    private void allowedMethodsWithPaymentCheck(Payment payment) {
        List<PaymentMethod> allowedMethodsWithBounced = Arrays.asList(PaymentMethod.CHEQUE, PaymentMethod.DIRECT_DEBIT);
        if (payment.getStatus() == PaymentStatus.BOUNCED && !allowedMethodsWithBounced.contains(payment.getMethodOfPayment())) {
            throw new BusinessException("Status Bounced can only be selected with Method of payment Cheque or Direct Debit");
        }

        List<PaymentStatus> allowedStatusWithDirectDebit = Arrays.asList(PaymentStatus.BOUNCED,
                PaymentStatus.DELETED, PaymentStatus.PDC, PaymentStatus.PRE_PDP, PaymentStatus.RECEIVED);
        if (payment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT) && !allowedStatusWithDirectDebit.contains(payment.getStatus())) {
            throw new BusinessException(payment.getStatus() + " is not allowed with type Direct Debit");
        }
    }

    public void sendNotification(Payment payment) {
        String subject = "New Discount Payment added to the contract  " + payment
                .getContract()
                .getLabel();

        String text = "Hi , A new Discount Payment of amount AED " + payment
                .getAmountOfPayment() + " has been added to the contract " + payment
                .getContract()
                .getLabel() + "  of the client " + payment.getContract()
                .getClient()
                .getLabel() + "Reason for Discount is " + payment
                .getReasonOfDiscount() + "";

        User user = Setup.getRepository(UserRepository.class).findOne(Long.valueOf(1));
        Notification notification = new Notification();
        notification.setTitle(subject);
        notification.setBody(text);
        notification.setUser(user);
        Setup.getApplicationContext().getBean(NotificationService.class)
                .sendNotification(notification, true);
    }

    @Transactional
    public Payment updatePayment(Payment p, boolean overridePermissions, boolean silentSave) {
        allowedMethodsWithPaymentCheck(p);

        Payment oldPayment = paymentRepository.findOne(p.getId());
        /*if ((!oldPayment.getDateOfPayment().equals(p.getDateOfPayment()) ||
                !oldPayment.getTypeOfPayment().getId().equals(p.getTypeOfPayment().getId()) ||
                !oldPayment.getAmountOfPayment().equals(p.getAmountOfPayment())) &&
                contractPaymentWrapperRepository.existsByGeneratedPaymentId(p.getId())) {
            throw new BusinessException("Please delete the payment and add another one");
        }*/

        boolean amountChanged = !oldPayment.getAmountOfPayment().equals(p.getAmountOfPayment());
        boolean dateChanged = !oldPayment.getDateOfPayment().equals(p.getDateOfPayment());
        boolean typeChanged = !oldPayment.getTypeOfPayment().getId().equals(p.getTypeOfPayment().getId());
        boolean methodChanged = !oldPayment.getMethodOfPayment().equals(p.getMethodOfPayment());

        if(amountChanged || dateChanged || typeChanged || methodChanged) {
            List<ContractPaymentWrapper> wrappers = contractPaymentWrapperRepository
                    .findByGeneratedPaymentId(p.getId());

            for (ContractPaymentWrapper wrapper : wrappers) {
                if(amountChanged) wrapper.setAmount(p.getAmountOfPayment());
                if (dateChanged) wrapper.setPaymentDate(p.getDateOfPayment());
                if(typeChanged) wrapper.setPaymentType(p.getTypeOfPayment());

                contractPaymentWrapperRepository.save(wrapper);

                ContractPayment contractPayment = wrapper.getContractPayment();
                if(amountChanged) contractPayment.setAmount(p.getAmountOfPayment());
                if (dateChanged) contractPayment.setDate(p.getDateOfPayment());
                if(typeChanged) contractPayment.setPaymentType(p.getTypeOfPayment());
                if(methodChanged) contractPayment.setPaymentMethod(p.getMethodOfPayment());

                contractPaymentRepository.save(contractPayment);

            }
        }

        removeMonthlyRelatedFlag(oldPayment, p);
        if (equalsExceptNotesAndAttachment(oldPayment, p) ||
                canUpdatePayment(oldPayment, p, p.getStatus()) ||
                overridePermissions) {

            p = silentSave ? paymentRepository.silentSave(p) : paymentRepository.save(p);

            if ("Discount".equals(p.getTypeOfPayment().getName())) sendNotification(p);

            return p;
        }

        return null;
    }

    // ACC-8313
    private void removeMonthlyRelatedFlag(Payment origin, Payment entity) {
        if (!entity.getTypeOfPayment().equals(origin.getTypeOfPayment()) &&
                !Arrays.asList("monthly_payment", "paid_the_client_-_refund", "partial_mp_refunded_to_client")
                        .contains(entity.getTypeOfPayment().getCode()) &&
                (origin.getIsInitial() || origin.getIncludeWorkerSalary() || origin.getIsProRated())) {
            entity.setIsInitial(false);
            entity.setIncludeWorkerSalary(false);
            entity.setIsProRated(false);
        }
    }

    public boolean equalsExceptNotesAndAttachment(Payment oldPayment, Payment payment) {
        return oldPayment.getStatus().equals(payment.getStatus()) &&
                oldPayment.getTypeOfPayment().getCode().equals(payment.getTypeOfPayment().getCode()) &&
                oldPayment.getDateOfPayment().equals(payment.getDateOfPayment()) &&
                oldPayment.getAmountOfPayment().equals(payment.getAmountOfPayment()) &&
                oldPayment.getMethodOfPayment().equals(payment.getMethodOfPayment()) &&
                ((oldPayment.getReasonOfBouncingCheque() != null && oldPayment.getReasonOfBouncingCheque().getCode().equals(payment.getReasonOfBouncingCheque().getCode()))
                        || (oldPayment.getReasonOfBouncingCheque() == null && payment.getReasonOfBouncingCheque() == null)) &&
                ((oldPayment.getDateOfBouncing() != null && oldPayment.getDateOfBouncing().equals(payment.getDateOfBouncing()))
                        || (oldPayment.getDateOfBouncing() == null && payment.getDateOfBouncing() == null)) &&
                ((oldPayment.getChequeNumber() != null && oldPayment.getChequeNumber().equals(payment.getChequeNumber()))
                        || (oldPayment.getChequeNumber() == null && payment.getChequeNumber() == null)) &&
                ((oldPayment.getChequeName() != null && oldPayment.getChequeName().equals(payment.getChequeName()))
                        || (oldPayment.getChequeName() == null && payment.getChequeName() == null)) &&
                ((oldPayment.getBankName() != null && oldPayment.getBankName().getCode().equals(payment.getBankName().getCode()))
                        || (oldPayment.getBankName() == null && payment.getBankName() == null));
    }

    // get method from cm PaymentBackgroundTasks
    public void applySilentSaveOnPayment(Long id , String status) {
        List<Payment> payments =  paymentRepository.findByDirectDebitIdAndStatus(id, PaymentStatus.valueOf(status));

        if (payments.isEmpty()) {
            logger.info("no related payments to delete");
            return;
        }
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        for (Payment p : payments) {
            logger.info("working on payment " + p.getId());
            p.setStatus(PaymentStatus.DELETED);
            p.setIsDeleted(true);
            updatePaymentSilent(p);
        }

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaidEndDate(payments.get(0).getContract());
    }

    // End moved from CM ACC-6103

    public Map<String, Object> maidVisaFlow(Payment entity, Contract contract) {
        logger.info("payment id: " + entity.getId());

        HistorySelectQuery<Contract> historyQuery = new HistorySelectQuery<>(Contract.class);
        historyQuery.filterBy("id", "=", contract.getId());
        historyQuery.filterByChanged("scheduledDateOfTermination");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        List<Contract> dhs = historyQuery.execute();

        Calendar contractTerminationCalendar = Calendar.getInstance();
        Calendar paymentCalendar = Calendar.getInstance();
        paymentCalendar.setTime(entity.getDateOfPayment());

        logger.log(Level.SEVERE, "dhs size -> " + dhs.size());
        Map<String, Object> map = new HashMap<>();

        if (!dhs.isEmpty()) {
            contractTerminationCalendar.setTime(contract.getDateOfTermination() == null ?
                    contract.getScheduledDateOfTermination() : contract.getDateOfTermination());

            if ((paymentCalendar.get(Calendar.YEAR) == contractTerminationCalendar.get(Calendar.YEAR) &&
                    paymentCalendar.get(Calendar.MONTH) > contractTerminationCalendar.get(Calendar.MONTH)) ||
                    paymentCalendar.get(Calendar.YEAR) > contractTerminationCalendar.get(Calendar.YEAR)) {

                if (entity.getStatus().equals(PaymentStatus.RECEIVED) &&
                        Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                                .contains(contract.getStatus())) {

                    logger.log(Level.SEVERE, "MAID VISA PAYMENT RECEIVED");

                    //we don't want money from client
                    SelectQuery<Payment> requiredForUnfitToWorkQuery = new SelectQuery(Payment.class);
                    requiredForUnfitToWorkQuery.filterBy("id", "=", entity.getId());
                    requiredForUnfitToWorkQuery.filterBy("requiredForUnfitToWork", "=", Boolean.TRUE);
                    List<Payment> requiredForUnfitToWorkList = requiredForUnfitToWorkQuery.execute();

                    boolean requiredForUnfitToWork = !requiredForUnfitToWorkList.isEmpty();
                    logger.log(Level.SEVERE, "requiredForUnfitToWork: " + requiredForUnfitToWork);
                    logger.log(Level.SEVERE, "replacementFor: " + (entity.getReplacementFor() != null && entity.getReplacementFor().getId() != null));

                    if (!requiredForUnfitToWork &&
                            (entity.getReplacementFor() == null || entity.getReplacementFor().getId() == null)) {

                        logger.log(Level.SEVERE, "Don't want money");

                        // if(!contract.isCancelledWithinFirstXDays()) {
                        // ACC-8121 Stop requesting a refund for any received payment after cancelation for MV contracts
                        /*Map responseMap = Setup.getApplicationContext()
                                .getBean(ClientMessagingAndRefundService.class)
                                .fullRefundProcess(entity.getContract(), entity.getContract().getClient(), entity.getAmountOfPayment(),
                                        entity.getClientInformedAboutRefund(), entity);

                        map.putAll(responseMap);
                        map.put("fullyRefunded", responseMap.get("refundSentToExpensify"));*/
                        //}
                    } else {
                        map.put("requiredForUnfitToWork", false);
                    }

                    createMaidVisaCancellation(contract, entity);
                } else {
                    // revised flow
                    map.putAll(Setup.getApplicationContext()
                            .getBean(PaymentDeletionRules.class)
                            .handlePayment(entity));
                    if (map.containsKey("status") && map.get("status") != null &&
                            map.get("status").equals(PaymentStatus.DELETED.getValue())) {
                        createMaidVisaCancellation(entity.getContract(), entity);
                    }
                }
            }
        }

        return map;
    }

    public Map<String, Object> maidCCFlow(Payment entity, Contract contract) {
        logger.info("payment id: " + entity.getId());

        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        ClientMessagingAndRefundService clientMessagingAndRefundService = Setup.getApplicationContext().getBean(ClientMessagingAndRefundService.class);

        Map<String, Object> map = new HashMap<>();
        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTerm_Contract(contract);

        // if client paying through cheques -> Do Nothing
        if (contract.getClient().isPayingThroughCheques(contract)) {
            logger.log(Level.SEVERE, "client paying through cheques ");
            return map;
        }

        // ACC-1321
        if (contract.getSettled() &&
                Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).
                        contains(contract.getStatus()) &&
                entity.getStatus().equals(PaymentStatus.RECEIVED)) {

            // full refund process
            //if(!contract.isCancelledWithinFirstXDays()) { ACC-7706
            logger.log(Level.SEVERE, "call full refund");

            Map responseMap = clientMessagingAndRefundService.fullRefundProcess(contract, contract.getClient(),
                    entity.getAmountOfPayment(), entity.getClientInformedAboutRefund(), entity);

            map.putAll(responseMap);
            map.put("fullyRefunded", responseMap.get("refundSentToExpensify"));
            //}

            return map;
        }

        // revised flow
        String dateToBeSent = DateUtil.formatDateDashed(
                Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus()) ?
                        contract.getDateOfTermination() :
                        contract.getScheduledDateOfTermination());

        Double clientBalance = Setup.getApplicationContext().getBean(ContractService.class)
                .getCorrectedBalance(contract.getId(), dateToBeSent);

        if (clientBalance != null) {
            if (clientBalance >= 1D) { // If we want money from the client
                logger.log(Level.SEVERE, "we want money");

                switch(entity.getStatus()) {
                    case BOUNCED:
                        logger.log(Level.SEVERE, "payment BOUNCED");
                        map.putAll(Setup.getApplicationContext()
                                .getBean(PaymentDeletionRules.class)
                                .handlePayment(entity));
                        break;

                    case RECEIVED:
                        logger.log(Level.SEVERE, "payment RECEIVED");

                        if (entity.getAmountOfPayment() > clientBalance - 1) {
                            logger.log(Level.SEVERE, "contract status -> " + contract.getStatus());

                            if (Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                                    .contains(contract.getStatus())) {

                                map.putAll(ccReceivedWantMoneyCancelled(entity, contract, clientBalance, dds));
                            } else {
                                map.putAll(ccReceivedWantMoneyNotCancelled(entity, contract, clientBalance));
                            }
                        }
                        break;
                }
            } else { // If we do not want money from the client
                logger.log(Level.SEVERE, "we don't want money");

                switch(entity.getStatus()) {
                    case RECEIVED:
                        logger.log(Level.SEVERE, "payment RECEIVED");

                        if (Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                                .contains(contract.getStatus())) {

                            logger.log(Level.SEVERE, "contract status -> " + contract.getStatus());

                            // ACC-1475
                            createDDFCToDos(dds, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);

                            Setup.getApplicationContext().getBean(ContractService.class)
                                    .markContractSettled(contract.getId());
                        }
                        logger.log(Level.SEVERE, "DDcToDo created");

                        //if(!contract.isCancelledWithinFirstXDays()) { ACC-7706
                        // full refund process
                        logger.log(Level.SEVERE, "call full refund");

                        Map responseMap = clientMessagingAndRefundService.fullRefundProcess(
                                contract, contract.getClient(),
                                entity.getAmountOfPayment(),
                                entity.getClientInformedAboutRefund(),
                                entity);

                        map.putAll(responseMap);
                        map.put("fullyRefunded", responseMap.get("refundSentToExpensify"));
                        //}
                        break;
                    case BOUNCED: // ACC-2720
                        logger.log(Level.SEVERE, "payment BOUNCED");

                        if (!Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                                .contains(contract.getStatus())) {

                            logger.log(Level.SEVERE, "Active Contract");
                            map.put("status", PaymentStatus.DELETED.getValue());
                            map.put("bouncedInCancellationWaitingPeriod", Boolean.TRUE);
                        } else {
                            map.putAll(Setup.getApplicationContext()
                                    .getBean(PaymentDeletionRules.class)
                                    .handlePayment(entity));
                        }
                        break;
                }
            }
        }

        return map;
    }

    public Map ccReceivedWantMoneyCancelled(
            Payment entity,
            Contract contract,
            Double clientBalance,
            List<DirectDebit> dds) {

        logger.log(Level.SEVERE, "ccReceivedWantMoneyCancelled started");
        Map map = new HashMap();

        if (entity.getAmountOfPayment() <= clientBalance - 1) {
            logger.log(Level.SEVERE, "we still need money -> do nothing");
            return map;
        }

        createDDFCToDos(dds, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);

        // ACC-1321
        Setup.getApplicationContext().getBean(ContractService.class)
                .markContractSettled(contract.getId());

//        if(contract.isCancelledWithinFirstXDays()) { ACC-7706
//            logger.log(Level.SEVERE, "Contract canceled within X Days -> exiting");
//            return map;
//        }

        if (entity.getAmountOfPayment() >= clientBalance + 1) {
            logger.log(Level.SEVERE, "Client still owes us a refund");

            ClientMessagingAndRefundService clientMessagingAndRefundService = Setup.getApplicationContext().getBean(ClientMessagingAndRefundService.class);

            Map<String, Object> proratedContractConditions = clientMessagingAndRefundService
                    .checkProratedContractConditions(contract, contract.getDateOfTermination());
            boolean returnedMaidPreviousMonth = (boolean) proratedContractConditions.get("returnedMaidPreviousMonth");
            boolean clientCancelledWithinXDays = (boolean) proratedContractConditions.get("clientCancelledWithinXDays");
            boolean doFullRefund = returnedMaidPreviousMonth || clientCancelledWithinXDays;

            if (!doFullRefund && entity.getIsInitial()) {
                // ACC-2085
                logger.log(Level.SEVERE, "doFullRefund: " + doFullRefund);
                doFullRefund = entity.getIsInitial() && entity.getIsProRated() && contract.getProRatedPlusMonth() &&
                        new DateTime(contract.getStartOfContract()).plusMonths(1).dayOfMonth().withMinimumValue().isAfterNow();
            }

            boolean paymentIsCurrentMonth = new DateTime(entity.getDateOfPayment()).plusHours(1).isAfter(
                    new DateTime().withDayOfMonth(1).withTimeAtStartOfDay());

            if (doFullRefund && paymentIsCurrentMonth) {
                logger.log(Level.SEVERE, "returnedMaidPreviousMonth: " + returnedMaidPreviousMonth +
                        "; clientCancelledWithinXDays: " + clientCancelledWithinXDays +
                        "; doFullRefund: " + doFullRefund);

                // full refund process
                Map responseMap = clientMessagingAndRefundService.fullRefundProcess(contract, contract.getClient(),
                        entity.getAmountOfPayment(), entity.getClientInformedAboutRefund(), entity);

                map.putAll(responseMap);
                map.put("fullyRefunded", responseMap.get("refundSentToExpensify"));
            } else if (!entity.getIsInitial()) {
                logger.log(Level.SEVERE, "not initial payment call partial refund");

                // partial refund process
                Map responseMap = clientMessagingAndRefundService.partialRefundProcess(contract, contract.getClient(),
                        entity.getAmountOfPayment() - clientBalance, entity.getClientInformedAboutRefund(), entity);

                map.putAll(responseMap);
                map.put("fullyRefunded", false);
            }
        }

        logger.log(Level.SEVERE, "ccReceivedWantMoneyCancelled Result");
        return map;
    }

    public Map ccReceivedWantMoneyNotCancelled(
            Payment entity,
            Contract contract,
            Double clientBalance) {

        logger.log(Level.SEVERE, "ccReceivedWantMoneyNotCancelled started");
        Map map = new HashMap();

        if (entity.getAmountOfPayment() <= clientBalance - 1) {
            logger.log(Level.SEVERE, "we still need money -> do nothing");
            return map;
        }

        // ACC-1297
        if (contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {
            logger.log(Level.SEVERE, "Indefinite CC contract -> don't do partial refund");
            return map;
        }

//        if(contract.isCancelledWithinFirstXDays()) { ACC-7706
//            logger.log(Level.SEVERE, "Cancelled within X Days -> existing");
//            return map;
//        }

        // partial refund process
        logger.log(Level.SEVERE, "call partial refund");
        Map responseMap = Setup.getApplicationContext().getBean(ClientMessagingAndRefundService.class)
                .partialRefundProcess(contract, contract.getClient(), entity.getAmountOfPayment() - clientBalance,
                        entity.getClientInformedAboutRefund(), entity);

        map.putAll(responseMap);
        map.put("fullyRefunded", false);

        logger.log(Level.SEVERE, "ccReceivedWantMoneyNotCancelled Result");
        return map;
    }

    // ACC-1475
    private void createDDFCToDos(
            List<DirectDebit> dds,
            DirectDebitCancellationToDoReason reason) {

        DirectDebitService ddService = Setup.getApplicationContext().getBean(DirectDebitService.class);

        for (DirectDebit dd : dds) {
            logger.log(Level.SEVERE, "cancelling dd with id -> " + dd.getId());
            if(!ddService.ddHasAllPastNonMonthlyPayment(dd)){ // ACC-3844
                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                        .cancelWholeDD(dd, reason);
            }
            logger.log(Level.SEVERE, "DDcToDo created");
        }
    }

    private void createMaidVisaCancellation(
            Contract contract,
            Payment entity) {

        if(!Setup.getRepository(PaymentRepository.class)
                .existsByContractAndRequiredAndIdNotIn(contract, Arrays.asList(entity.getId()))) {

            createDDFCToDos(
                    Setup.getRepository(DirectDebitRepository.class).findByContractPaymentTerm_Contract(contract),
                    DirectDebitCancellationToDoReason.CONTRACT_CANCELLATION);
        }
    }

    //ACC-3920
    public void deleteBouncedPayment(Payment bouncedPayment) {
        bouncedPayment.setStatus(PaymentStatus.DELETED);

        forceUpdatePayment(bouncedPayment);
    }
    
    public Map<String, Object> processACC3968(Payment entity) {
        logger.info("processACC3968 triggered for: " + entity.getId());
        
        Map<String, Object> toReturn = new HashMap();
        List<ContractPaymentWrapper> contractPaymentWrappers = contractPaymentWrapperRepository.findForAcc3968(entity.getId());

        // ACC-7697
        ContractPaymentWrapperRepository contractPaymentWrapperRepository = Setup.getRepository(ContractPaymentWrapperRepository.class);
        ContractPaymentWrapper wrapper = entity.getParentWrapperId() != null ?
                contractPaymentWrapperRepository.findOne(entity.getParentWrapperId()) : null;
        /*if (wrapper != null && wrapper.getReplacedBouncedPaymentId() != null && wrapper.getGeneratedPaymentId() == null) {
            wrapper.setGeneratedPaymentId(entity.getId());
            contractPaymentWrapperRepository.save(wrapper);
        } else */
        if (contractPaymentWrappers.isEmpty() && entity.getParentWrapperId() != null) {
            logger.info("processACC3968 adding parentWrapperId: " + entity.getParentWrapperId());
            contractPaymentWrappers.add(contractPaymentWrapperRepository.findOne(entity.getParentWrapperId()));
        }
        
        Payment relatedPayment = null;
        
        if(!contractPaymentWrappers.isEmpty()){
            logger.info("processACC3968 if");
            
            for (ContractPaymentWrapper contractPaymentWrapper : contractPaymentWrappers){
                if(contractPaymentWrapper.getReplacedFuturePaymentId() == null) {
                    logger.info("processACC3968 replacedFuturePaymentId is null");
                    processACC5241(entity, contractPaymentWrapper);
                    continue;
                }
                relatedPayment = paymentRepository.findOne(contractPaymentWrapper.getReplacedFuturePaymentId());

                if(relatedPayment != null){
                    DirectDebit dd = relatedPayment.getDirectDebit();
                    
                    if(dd != null){
                        logger.info("processACC3968 dd: " + dd.getId());
                        
                        if(dd.getCategory().equals(DirectDebitCategory.B)){
                            toReturn.putAll(processRelatedPaymentIfReceivedOrBounced(relatedPayment, entity));
                        } else {
                            cancelDDAndAllCollectionFlows(relatedPayment , dd);
                        }
                    }
                }
            }
        } else {
            logger.info("processACC3968 else");
            
            contractPaymentWrapperRepository.findByReplacedFuturePaymentId(entity.getId())
                    .stream().filter(c -> c.getGeneratedPaymentId() != null)
                    .map(c -> paymentRepository.findOne(c.getGeneratedPaymentId()))
                    .filter(p -> p.getStatus().equals(PaymentStatus.RECEIVED))
                    .forEach(p -> processRelatedReceivedPayment(p, entity));  
        }
        
        addPossibleRefund(entity);
        toReturn.putAll(tagPossibleReplacement(entity));
        
        return toReturn;
    }

    // ACC-5241
    private void processACC5241(
            Payment payment,
            ContractPaymentWrapper contractPaymentWrapper){

        List<DirectDebitStatus> ignoredStatus=
                Arrays.asList(DirectDebitStatus.EXPIRED,
                        DirectDebitStatus.PENDING_FOR_CANCELLATION,
                        DirectDebitStatus.CANCELED);

        List<DirectDebit> dds = directDebitRepository.findForAcc5241(
                payment.getContract(), ignoredStatus, payment.getDateOfPayment(),
                payment.getAmountOfPayment(), payment.getTypeOfPayment());

        if(dds.isEmpty()) {
            logger.log(Level.INFO, "NO DD matched");
            return;
        }

        logger.log(Level.INFO, "Matched DD: " + dds.get(0).getId());

        if(dds.get(0).getContractPayments().size() == 1) {
            logger.log(Level.INFO, "Matched one payment only");
            cancelDDAndAllCollectionFlows(null, dds.get(0));
            return;
        }

        boolean allPaymentReceived = dds.get(0).getContractPayments().stream()
                    .filter(cp -> !(payment.getAmountOfPayment().equals(cp.getAmount())
                            && payment.getTypeOfPayment().equals(cp.getPaymentType())))
                    .allMatch(cp -> paymentRepository
                            .existsByContractAndAmountOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetweenAndStatus(
                                    cp.getContractPaymentTerm().getContract(),
                                    cp.getAmount(),
                                    cp.getPaymentType().getCode(),
                                    dds.get(0).getStartDate(),
                                    dds.get(0).getExpiryDate(),
                                    PaymentStatus.RECEIVED));

        logger.log(Level.INFO, "allPaymentReceived #1: " + allPaymentReceived);
        if(allPaymentReceived) {
            cancelDDAndAllCollectionFlows(null, dds.get(0));
            return;
        }

        allPaymentReceived = dds.get(0).getContractPayments().stream()
                .filter(cp -> !(payment.getAmountOfPayment().equals(cp.getAmount())
                        && payment.getTypeOfPayment().equals(cp.getPaymentType())))
                .allMatch(cp -> contractPaymentWrapperRepository
                        .existsByContractPaymentConfirmationToDoAndContractPayment_AmountAndContractPayment_PaymentType(
                                contractPaymentWrapper.getContractPaymentConfirmationToDo(),
                                cp.getAmount(), cp.getPaymentType()));

        logger.log(Level.INFO, "allPaymentReceived #2: " + allPaymentReceived);
        if(allPaymentReceived) cancelDDAndAllCollectionFlows(null, dds.get(0));
    }

    public void pauseBouncingFlowTagReplaced(Payment payment) {
        payment.setReplaced(true);
        payment.setBouncedFlowPausedForReplacement(true);
        payment.setBouncedFlowPausedFromDate(new java.util.Date());

        forceUpdatePayment(payment);
    }

    private Map<String, Object> tagPossibleReplacement(Payment entity) {
        logger.info("ID: " + entity.getId());

        Map<String, Object> output = new HashMap<>();
        List<Payment> bouncedPayments = paymentRepository.findByStatusAndContractAndDateOfPaymentAndAmountOfPaymentAndTypeOfPaymentAndReplaced(
                PaymentStatus.BOUNCED, entity.getContract(), entity.getDateOfPayment(), 
                entity.getAmountOfPayment(), entity.getTypeOfPayment(), false);

        if (bouncedPayments.isEmpty()) {
            logger.info("find by wrapper if the amount didn't match");
            bouncedPayments = paymentRepository.findByReplacedBouncedPaymentId(entity.getId());
        }

        if(bouncedPayments.isEmpty()) {
            logger.info("no bounced payments");
            return output;
        }

        bouncedPayments.forEach(p -> {
            logger.info("tagging payment replaced and pausing bounced flow payment id: " + p.getId());
            pauseBouncingFlowTagReplaced(p);
        });

        if(entity.getReplacementFor() == null) {
            logger.info("Replacement for is null");
            output.put("isReplacement", true);
            output.put("replacementFor", bouncedPayments.get(0));
        }
        
        return output;
    }
    
    private void addPossibleRefund(Payment entity) {
        logger.info("addPossibleRefund");
        
        if(clientRefundToDoRepository.existsByRelatedPaymentId(entity.getId())) {
            logger.info("addPossibleRefund already refunded -> exiting");
            return;
        }

        boolean refund = QueryService.existsEntity(Payment.class,
                "e.contract.id = :p0 and e.id <> :p1 and e.status = :p2 and e.dateOfPayment = :p3 and " +
                        "e.typeOfPayment = :p4 and e.amountOfPayment = :p5",
                new Object[]{entity.getContract().getId(), entity.getId(), PaymentStatus.RECEIVED,
                        entity.getDateOfPayment(), entity.getTypeOfPayment(), entity.getAmountOfPayment()});
        
        if(refund) {
            logger.info("addPossibleRefund refunding");
            
            Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
            
            clientMessagingAndRefundService.addClientRefund(contract, contract.getClient(),
                    entity.getAmountOfPayment(), ClientRefundRequestType.ERP, 
                    AccountingModule.PAYMENT_REQUEST_PURPOSE_DUPLICATED_PAYMENT, entity, "Payment Received Event flow");
        }
    }
    
    public boolean replacementPaymentUpdated(Payment entity) {
        logger.info("replacementPaymentUpdated");

        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");
        
        List<ContractPaymentWrapper> contractPaymentWrapper = contractPaymentWrapperRepository
                .findByReplacedFuturePaymentId(entity.getId());
        
        Boolean replaced = contractPaymentWrapper.stream()
                .filter(c -> c.getGeneratedPaymentId() != null)
                .anyMatch(c -> {
                    DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());

                    if(dd != null){
                        Payment relatedPayment = paymentRepository.findOne(c.getGeneratedPaymentId());
                        
                        if(dd.getCategory().equals(DirectDebitCategory.B)) {

                            if (relatedPayment.getTypeOfPayment().equals(monthlyPayment)
                                    && relatedPayment.getStatus().equals(PaymentStatus.RECEIVED)) {

                                logger.info("processACC3968 replacementPaymentUpdated: replacing bounced payment");
                                return replacePayment(relatedPayment, entity);
                            }
                        } else {
                            //ACC-5050
                            if (relatedPayment.getStatus().equals(PaymentStatus.RECEIVED)) {
                                logger.log(Level.INFO, "cancel dd id : {0}", dd.getId());
                                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                                    .cancelWholeDD(dd, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
                                return replacePayment(relatedPayment, entity);
                            }
                        }
                    }
                    return false;
                });
        
        if(!replaced) {
            logger.info("checking matching received payments");
            List<Payment> receivedPayments = paymentRepository.findByStatusAndContractAndDateOfPaymentAndAmountOfPaymentAndTypeOfPayment(
                    PaymentStatus.RECEIVED, entity.getContract(), entity.getDateOfPayment(), 
                    entity.getAmountOfPayment(), entity.getTypeOfPayment());
            
            logger.info("found: " + receivedPayments.size());
            
            if(!receivedPayments.isEmpty() && receivedPayments.get(0).getReplacementFor() == null)
                return replacePayment(receivedPayments.get(0), entity);
        }
        
        return replaced;
    }
    
    private Map<String, Object> processRelatedPaymentIfReceivedOrBounced(
            Payment relatedPayment,
            Payment receivedPayment){

        logger.info("processRelatedPaymentIfReceivedOrBounced");
        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");
        
        Map<String, Object> toReturn = new HashMap();
        if(relatedPayment.getTypeOfPayment().equals(monthlyPayment) ){
            if(relatedPayment.getStatus().equals(PaymentStatus.RECEIVED)){
                logger.info("refund");
                
                clientMessagingAndRefundService.addClientRefund(relatedPayment.getContract(), relatedPayment.getContract().getClient(),
                        relatedPayment.getAmountOfPayment(), ClientRefundRequestType.ERP, 
                        AccountingModule.PAYMENT_REQUEST_PURPOSE_DUPLICATED_PAYMENT,
                        receivedPayment, "Payment Received Event flow");

            } else if(relatedPayment.getStatus().equals(PaymentStatus.BOUNCED) &&
                    receivedPayment.getReplacementFor() == null){

                logger.info("pause bouncing flow");
                pauseBouncingFlowTagReplaced(relatedPayment);

                toReturn.put("isReplacement", true);
                toReturn.put("replacementFor", relatedPayment);
            }
        }
        
        return toReturn;
    }
    
    public Boolean replacePayment(
            Payment replacingPayment,
            Payment bouncedPayment) {
        logger.info("replacePayment -> replacing : " + bouncedPayment.getId() +
                " with " + replacingPayment.getId());

        replacingPayment.setIsReplacement(true);
        replacingPayment.setReplacementFor(bouncedPayment);
        forceUpdatePayment(replacingPayment);
        return true;
    }
    
    private void processRelatedReceivedPayment(Payment relatedGeneratedPayment, Payment receivedPayment){
        logger.info("processACC3968 processRelatedReceivedPayment");

        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");
        
        if(relatedGeneratedPayment.getTypeOfPayment().equals(monthlyPayment)){
            logger.info("processACC3968 processRelatedReceivedPayment: refund");
            
            clientMessagingAndRefundService.addClientRefund(relatedGeneratedPayment.getContract(), relatedGeneratedPayment.getContract().getClient(),
                    relatedGeneratedPayment.getAmountOfPayment(), 
                    ClientRefundRequestType.ERP, AccountingModule.PAYMENT_REQUEST_PURPOSE_DUPLICATED_PAYMENT,
                receivedPayment, "Payment Received Event flow");
        }

        //ACC-5050
        DirectDebit dd = receivedPayment.getDirectDebit();
        if (dd != null && dd.getId() != null) {
            dd = directDebitRepository.findOne(dd.getId());
            if (dd.getCategory().equals(DirectDebitCategory.A)) {
                logger.log(Level.INFO, "cancel dd id : {0}", dd.getId());
                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                    .cancelWholeDD(dd, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
            }
        }
    }

    // Acc-3968, Acc-5050
    private void cancelDDAndAllCollectionFlows(Payment relatedPayment, DirectDebit dd) {
        logger.info("processACC3968 cancelDDAndAllCollectionFlows");

        boolean proceed = relatedPayment == null ||
                (!relatedPayment.getSentToBankByMDD() &&
                (relatedPayment.getStatus().equals(PaymentStatus.PDC)
                        || relatedPayment.getStatus().equals(PaymentStatus.PRE_PDP)));

        if(!proceed) return;

        logger.info("processACC3968 cancelDDAndAllCollectionFlows: cancelling");

        // cancel related DDA and stop all collection flows
            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                    .cancelWholeDD(dd, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
    }

    @Transactional
    public void setPaymentSentToBank(Long paymentId) {
        Payment p = paymentRepository.findOne(paymentId);
        p.setSentToBankByMDD(true);
        updatePaymentDeepSilentSave(p);
    }

    //ACC-5056
    public DateTime getLastReceivedMonthlyPaymentDate(Contract contract) {
        logger.info("contract id: " + contract.getId());

        return getLastReceivedPaymentDateByType(contract, "monthly_payment");
    }

    public DateTime getLastReceivedPaymentDateByType(Contract contract, String typeOfPayment) {
        Payment payment = paymentRepository
                .findFirstByContractAndStatusAndTypeOfPayment_CodeOrderByDateOfPaymentDesc(
                        contract, PaymentStatus.RECEIVED, typeOfPayment);
        if (payment == null) {
            logger.info("No received payments type: " + typeOfPayment);
            return null;
        }

        logger.log(Level.INFO, "contract id : {0}; payment date : {1}",
                new Object[]{contract.getId(), payment.getDateOfPayment()});

        return new DateTime(payment.getDateOfPayment()).withTimeAtStartOfDay();
    }

    //ACC-4905
    public boolean hasOneMonthlyPaymentReceived(Contract contract) {
        return paymentRepository.existsMonthlyPaymentReceived(contract.getId(), null);
    }

    public void updateDirectDebitFile(List<String> paymentIds, List<String> ddfIds) throws Exception {
        for (int i = 0; i < paymentIds.size(); i++) {
            Payment payment = paymentRepository.findOne(Long.valueOf(paymentIds.get(i)));
            payment.setDirectDebitFileId(Long.valueOf(ddfIds.get(i)));

            forceUpdatePayment(payment);
        }
    }

    public Transaction createTransactionForPayment(Payment entity) {

        return createTransactionForPayment(entity, entity.getStatus());
    }

    public Transaction createTransactionForPayment(Payment entity, PaymentStatus paymentStatus) {

        if (entity.getId() != null) {
            Transaction t = Setup.getRepository(TransactionRepository.class).findFirstByPaymentId(entity.getId());
            if (t != null) {
                logger.log(Level.SEVERE, "payment: " + entity.getId() + " already has related transaction");
                return t;
            }
        }

        // ACC-7050 ignore the payment with zero amount
        if (entity.getAmountOfPayment() == 0) {
            return null;
        }

        Contract contract = Setup.getRepository(ContractRepository.class)
                .findOne(entity.getContract().getId());

        TransactionPostingRuleRepository transactionPostingRuleRepository = Setup.getRepository(TransactionPostingRuleRepository.class);
        List<TransactionPostingRule> transactionPostingRules = transactionPostingRuleRepository
                .findByCompanyAndTypeOfPaymentAndMethodOfPaymentAndInitialPaymentAndPaymentStatusAndActiveTrue(
                        contract.getContractProspectType(), entity.getTypeOfPayment(),
                        entity.getMethodOfPayment(), entity.getIsInitial(), paymentStatus);

        if (transactionPostingRules.isEmpty() && entity.getIsInitial() && !PaymentHelper.isMonthlyPayment(entity.getTypeOfPayment())) {
            transactionPostingRules = transactionPostingRuleRepository
                    .findByCompanyAndTypeOfPaymentAndMethodOfPaymentAndInitialPaymentAndPaymentStatusAndActiveTrue(
                            contract.getContractProspectType(), entity.getTypeOfPayment(),
                            entity.getMethodOfPayment(), false, paymentStatus);
        }

        if (transactionPostingRules.size() != 1) {
            logger.log(Level.INFO, "contract type: {0}; payment type: {1}; method: {2}; initial: {3}; status: {4};",
                    new Object[]{contract.getContractProspectType(), entity.getTypeOfPayment(),
                            entity.getMethodOfPayment(), entity.getIsInitial(), paymentStatus});

            // ACC-8752
            if (paymentStatus.equals(PaymentStatus.RECEIVED)) {
                String parameterValue = Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_PAYMENT_TYPES_DONT_REQUIRE_POSTING_RULE);

                if (parameterValue == null || parameterValue.isEmpty() || !Arrays.stream(parameterValue.split(";"))
                        .collect(Collectors.toList()).contains(entity.getTypeOfPayment().getCode())) {
                    BackgroundTaskHelper.createBGTSendEmailForMissingTransactionPostingRule(
                            contract, entity.getTypeOfPayment(), entity.getMethodOfPayment(),
                            entity.getIsInitial(), paymentStatus, entity.getId());
                }
            }
            return null;
        }

        logger.log(Level.INFO, "payment id {0}", entity.getId());
        TransactionPostingRule transactionPostingRule = transactionPostingRules.get(0);

        // creating corresponding transaction for the payment
        // data from payment
        Transaction transaction = new Transaction();
        transaction.setPaymentId(entity.getId());

        // if revenue negative amount is checked -> post the payment's amount as negative value
        if (transactionPostingRule.getPostAs().equals(TransactionPostingRulePostAs.REVENUE) && transactionPostingRule.getRevenueNegativeAmount()) {
            logger.log(Level.SEVERE, "Negative Amount");
            transaction.setAmount((-1D) * Math.abs(entity.getAmountOfPayment()));
        } else {
            logger.log(Level.SEVERE, "Positive Amount");
            transaction.setAmount(Math.abs(entity.getAmountOfPayment()));
        }

        List<Attachment> transactionAttachments = entity.getAttachments();
        if (transactionAttachments != null && !transactionAttachments.isEmpty()) {
            logger.log(Level.SEVERE, "execute, attachements count: {0}", transactionAttachments.size());

            for (Attachment att : transactionAttachments) {
                logger.log(Level.SEVERE, "execute, attachements old ID: {0}", att.getId());
                Attachment newAttachment = Storage.storeTemporary(att.getName(),
                        Storage.getStream(att), "tr_" + att.getTag(), Boolean.FALSE);
                transaction.addAttachment(newAttachment);
            }
        }

        logger.log(Level.SEVERE, "attachments added successfully");
        logger.log(Level.SEVERE, "VAT: {0}", entity.getVat());

        if (entity.getVat() != null) {
            long vatSign = transactionPostingRule.isPositiveVat() ? 1 : -1;
            transaction.setVatAmount(vatSign * entity.getVat());
        }

        String transactionDescription;
        if (transactionPostingRule.getSameAsPaymentNote() != null && transactionPostingRule.getSameAsPaymentNote()) {
            transactionDescription = entity.getNote();
        } else {
            transactionDescription = this.setUpTransactionDescription(transactionPostingRule, entity, contract);
        }
        transaction.setDescription(transactionDescription);
        logger.log(Level.SEVERE, "description added successfully: {0}", transaction.getDescription());

        // data from transaction posting rule
        transaction.setDate(new java.sql.Date(new Date().getTime()));
        transaction.setPaymentType(transactionPostingRule.getTransactionMethodOfPayment());
        transaction.setFromBucket(transactionPostingRule.getFromBucket());
        transaction.setExpense(transactionPostingRule.getExpense());
        transaction.setToBucket(transactionPostingRule.getToBucket());
        transaction.setRevenue(transactionPostingRule.getRevenue());
        transaction.setVatType(transactionPostingRule.getVatType());
        transaction.setLicense(transactionPostingRule.getLicense());
        transaction.setTransactionType(transactionPostingRule.getTransactionFor());

        if (transactionPostingRule.getTransactionFor() != null) {
            switch (transactionPostingRule.getTransactionFor()) {
                case CONTRACT:
                    transaction.getContracts().add(
                            new ContractTransaction(transaction, contract));
                    break;
                case CLIENT:
                case PROSPECT:
                    transaction.getClients().add(
                            new ClientTransaction(transaction, contract.getClient()));
                    break;
            }
        }

        // ACC-1728
        if (entity.getUpdatedFromBankStatement() != null && entity.getUpdatedFromBankStatement()) {
            transaction.setAutomatic(true);
        }

        // ACC-1875
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old;
        if (oldPayments != null && !oldPayments.isEmpty()) {
            old = oldPayments.get(0);
            logger.log(Level.SEVERE, "old status: {0}", old.getStatus().getValue());

            // ACC-2563
            DateTime now = new DateTime();
            Integer dayOfMonthToChangePNLDate = Integer.parseInt(Setup.getParameter(
                    Setup.getCurrentModule(), AccountingModule.PARAMETER_PNL_CHANGE_CUTOFF_DAY));
            PicklistItem monthlyPayment = PicklistHelper.getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                    AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

            if (contract.getContractProspectType().getCode() != null &&
                    contract.getContractProspectType().getCode().equals(
                            PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)) &&
                    entity.getTypeOfPayment().getId().equals(monthlyPayment.getId()) &&
                    entity.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT) &&
                    paymentStatus != null &&
                    paymentStatus.equals(PaymentStatus.RECEIVED) &&
                    old.getStatus() != null &&
                    old.getStatus().equals(PaymentStatus.PDC) &&
                    (!entity.getRequiredForUnfitToWork() || !entity.getRequiredForBouncing()) &&
                    now.getDayOfMonth() <= dayOfMonthToChangePNLDate) {

                transaction.setPnlValueDate(now.minusMonths(1).dayOfMonth().withMaximumValue().toDate());
            }
        }

        transaction = (Transaction) Setup.getApplicationContext().getBean(TransactionsController.class)
                .newCreateEntity(transaction).getBody();

        // ACC-1728
        logger.log(Level.SEVERE, "getUpdatedFromBankStatement: {0}", entity.getUpdatedFromBankStatement());
        logger.log(Level.SEVERE, "transaction ID: {0}", transaction.getId());

        if (entity.getUpdatedFromBankStatement() != null && entity.getUpdatedFromBankStatement()) {
            Setup.getApplicationContext()
                    .getBean(TransactionService.class)
                    .updateTransaction(transaction, entity.getBankStatmentTransactionId());
        }

        return transaction;
    }

    private String setUpTransactionDescription(TransactionPostingRule transactionPostingRule, Payment payment, Contract paymentContract) {
        Client client = paymentContract.getClient();
        Map<String, Object> params = new HashMap();

        if (paymentContract != null && paymentContract.getId() != null) {
            params.put("@contract_id@", paymentContract.getId());
        } else {
            params.put("@contract_id@", "");
        }

        if (client != null) {
            params.put("@client_name@", client.getName() != null ? client.getName() : "");
        } else {
            params.put("@client_name@", "");
        }

        if (payment.getDirectDebit() != null && payment.getDirectDebit().getId() != null) {
            params.put("@direct_debit_id@", payment.getDirectDebit().getId());
        } else {
            params.put("@direct_debit_id@", "");
        }

        params.put("@payment_id@", payment.getId());
        // ACC-1557
        params.put("@payment_date@", payment.getDateOfPayment());

        // ACC-2991
        if (payment.getClientRefundToDo() != null && payment.getClientRefundToDo().getId() != null) {
            ClientRefundTodoRepository clientRefundTodoRepo = Setup.getRepository(ClientRefundTodoRepository.class);
            ClientRefundToDo clientRefundToDo = clientRefundTodoRepo.findOne(payment.getClientRefundToDo().getId());

            logger.info("Payment Refund Tod ID: " + clientRefundToDo.getId());
            params.put("@purpose_of_refund@", clientRefundToDo.getPurpose() != null ? clientRefundToDo.getPurpose().getName() : "");
            params.put("@notes@", clientRefundToDo.getNotes() != null ? clientRefundToDo.getNotes() : ""); // ACC-3394
        } else {
            logger.info("Payment Refund Tod ID IS NULL");
            params.put("@purpose_of_refund@", "");
        }

        String executed = transactionPostingRule.getDescription();

        for (Map.Entry<String, Object> param : params.entrySet()) {
            if (param != null && param.getKey() != null && param.getValue() != null) {
                executed = executed.replaceFirst(param.getKey(), param.getValue().toString());
            }
        }

        return executed;
    }

    public Payment replacePayment(Payment payment, boolean ignoreDDInfo) {

        Payment temp = paymentRepository.findOne(payment.getBouncedPaymentId());
        temp.setReplaced(true);
        forceUpdatePayment(temp);

        payment.setReplacementFor(temp);
        payment.setIsInitial(temp.getIsInitial());
        payment.setIsReplacement(true);
        payment.setWorkerSalary(temp.getWorkerSalary());
        payment.setIsProRated(temp.getIsProRated());
        if(!ignoreDDInfo){
            payment.setDirectDebitId(temp.getDirectDebitId());
            payment.setDirectDebitFileId(temp.getDirectDebitFileId());
        }
        return forceCreatePayment(payment);
    }

    public Payment createPayment(Payment p) {
        return createPayment(p, false, false);
    }

    public Payment createPayment(Payment p, boolean overridePermissions) {
        return createPayment(p, overridePermissions, false);
    }

    public Payment createPaymentSilent(Payment p) {
        return createPayment(p, false, true);
    }

    public Payment forceCreatePayment(Payment p) {
        return createPayment(p, true);
    }

    @Transactional
    public void updatePaymentDeepSilentSave(Payment p) {
        p.setDeepSilentSave(true);
        updatePaymentSilent(p);
    }
    public Payment updatePaymentSilent(Payment p) {
        return updatePayment(p, true, true);
    }

    public Payment updatePayment(Payment p) {
        return updatePayment(p, false, false);
    }

    public List<Payment> forceUpdatePayment(List<Payment> payments) {
        return payments.stream().map(this::forceUpdatePayment).collect(Collectors.toList());
    }

    public Payment forceUpdatePayment(Payment p) {
        return updatePayment(p, true, false);
    }

    @Transactional
    public Object updatePaymentDirectCall(Map payment) throws Exception {
        logger.info("update Payment");

        payment.put("overridePermissions", true);

        logger.info("payment map keys: " + payment.keySet());
        logger.info("payment map values: " + payment.values());

        Object response = moduleConnector.call("accounting", "paymentController", "update", Object.class, new Class[]{Map.class}, payment);
        logger.info("completed");

        return response;
    }

    public void deletePayment(Long paymentId) {
        logger.log(Level.INFO, "Deleting payment id: {0}", paymentId);
        Payment p = paymentRepository.findOne(paymentId);
        p.setStatus(PaymentStatus.DELETED);
        forceUpdatePayment(p);
    }

    public void pauseBouncingFlow(Long paymentId) {
        Payment payment = paymentId != null ? paymentRepository.findOne(paymentId) : null;
        if (payment == null) {
            throw new RuntimeException("Payment not Found");
        }

        if (!payment.getStatus().equals(PaymentStatus.BOUNCED)) {
            return;
            // ACC-7512
            // throw new RuntimeException(String.format("Payment with id (%s) is not Bounced", paymentId.toString()));
        }

        Integer numberOfPausedBouncedPaymentPerMonthAndContract = getNumberOfPausedBouncedPaymentPerMonthAndContract(payment);
        Integer maxNumberOfPausedBouncedPaymentPerMonthAndContract = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MAXIMUM_BOUNCED_PAYMENT_PAUSE_TIALS));


        if (numberOfPausedBouncedPaymentPerMonthAndContract >= maxNumberOfPausedBouncedPaymentPerMonthAndContract) {
            logger.warning(String.format("Number of Paused Bounced Payments per Contract And Month is (%s) which exceeds the Max Number (%s), -> Don't Pause the Bouncing Flow",
                numberOfPausedBouncedPaymentPerMonthAndContract.toString(), maxNumberOfPausedBouncedPaymentPerMonthAndContract.toString()));
            return;
        }
        logger.info("payment id: " + paymentId);

        payment.setBouncedFlowPausedForReplacement(Boolean.TRUE);
        payment.setBouncedFlowPausedFromDate(new java.util.Date());
        forceUpdatePayment(payment);
    }

    public Integer getNumberOfPausedBouncedPaymentPerMonthAndContract(Payment payment) {
        Date startOfMonth = DateUtil.getFirstOfMonthDate();
        Date endOfMonth = DateUtil.getEndOfMonthDate();

        HistorySelectQuery selectQuery = new HistorySelectQuery(Payment.class);
        selectQuery.filterBy("contract", "=", payment.getContract());
        selectQuery.filterBy("lastModificationDate", ">=", startOfMonth);
        selectQuery.filterBy("lastModificationDate", "<=", endOfMonth);

        selectQuery.filterByChanged("bouncedFlowPausedForReplacement");
        selectQuery.filterBy("bouncedFlowPausedForReplacement", "=", Boolean.TRUE);

        AggregateQuery aggQuery = new AggregateQuery(selectQuery, Aggregate.Count, "id");

        return aggQuery.execute().intValue();
    }

/*
    @Transactional
    public Map createPaymentsFromClientModule(Map payment, boolean forceCreate) {
        ObjectMapper objectMapper = Setup.getApplicationContext()
                .getBean(ObjectMapperConfiguration.class).objectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        logger.log(Level.INFO, "payment map keys: " + payment.keySet());
        logger.log(Level.INFO, "payment map values: " + payment.values());

        Payment paymentEntity = objectMapper.convertValue(payment, Payment.class);
        if (!paymentEntity.canUpdatePaymentByStatus(PaymentStatus.RECEIVED) && !forceCreate) {
            throw new RuntimeException("You don't have permission to create a Payment.");
        }

        Map response = null;
        try {
            response = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                    .call("clientmgmt", "paymentController",
                            "createFromJson", Map.class, new Class[]{Map.class}, payment);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("error while updating payment in 'createPaymentsFromClientModule' -> " + e.getMessage());
        }
        if (response != null) {
            logger.log(Level.INFO, "response:\n{0}", response.toString());
            return response;
        }
        return new HashMap();
    }
*/

   /* // ACC-1283
    @Transactional
    public void updatePaymentFromCmAsync(Map payment) {
        logger.info("update Payment");
        if (payment == null) return;

        payment.put("overridePermissions", true);

        logger.info("payment map keys: " + payment.keySet());
        logger.info("payment map values: " + payment.values());

        moduleConnector.postJsonAsync("clientmgmt/Payments/update", payment);
        logger.info("completed");
    }
*/
 /*   // ACC-1283
    @Transactional
    public Object updatePaymentFromCmDirectCall(Map payment) throws Exception {
        logger.info("update Payment");

        payment.put("overridePermissions", true);

        logger.info("payment map keys: " + payment.keySet());
        logger.info("payment map values: " + payment.values());

        Object response = moduleConnector.call("clientmgmt", "paymentController", "update", Object.class, new Class[]{Map.class}, payment);
        logger.info("completed");

        return response;
    }
*/

    @Transactional
    public ContractPaymentConfirmationToDo createToDoIfNotExists(
            Payment p, ContractPaymentTerm cpt, ContractPaymentConfirmationToDo.Source source) {
        try {
            logger.log(Level.INFO,"payments id: {0}", p.getId());

            ContractPaymentConfirmationToDo toDo = null;
            ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
            switch (source) {
                case BOUNCED_PAYMENT_FLOW:
                    toDo = contractPaymentConfirmationToDoRepository
                        .findToDoForBouncedPaymentFlow(p.getId());
                    wrapper.setReplacedBouncedPaymentId(p.getId());
                    break;
                case FAQ:
                    toDo = contractPaymentConfirmationToDoRepository
                            .findToDoForFAQ(p.getId());
                    wrapper.setReplacedFuturePaymentId(p.getId());
                    break;
                default:
                    if (Arrays.asList(PaymentStatus.PRE_PDP, PaymentStatus.PDC).contains(p.getStatus())) {
                        wrapper.setGeneratedPaymentId(p.getId());
                    }
            }

            if (toDo != null) return toDo;

            toDo = new ContractPaymentConfirmationToDo();
            toDo.setContractPaymentTerm(cpt);
            toDo.setSource(source);
            toDo.setPaymentType(p.getTypeOfPayment());
            toDo.setPaymentMethod(PaymentMethod.CARD);
            toDo.setPayingOnline(true);

            ContractPaymentType type = cpt.getContractPaymentTypes()
                    .stream()
                    .filter(t -> t.getType().getCode().equals(p.getTypeOfPayment().getCode()))
                    .findFirst()
                    .orElse(null);
            String description = type != null ? type.getDescription() : p.getTypeOfPayment().getName();
            toDo.setDescription(description);
            toDo.setAttachments(p.getAttachments());

            wrapper.setContractPaymentConfirmationToDo(toDo);
            wrapper.setPaymentDate(p.getDateOfPayment());
            wrapper.setProrated(p.getIsProRated());
            wrapper.setInitial(p.getIsInitial());
            wrapper.setActualReceivedAmount(p.getAmountOfPayment());
            wrapper.setAmount(p.getAmountOfPayment());
            wrapper.setVatPaidByClient(p.getVat() != null && !p.getVat().equals(0.0));
            wrapper.setIncludeWorkerSalary(p.getIncludeWorkerSalary());
            // ACC-8422
            wrapper.setWorkerSalary(p.getWorkerSalary());
            wrapper.setDiscountAmount(p.getDiscount());
            wrapper.setDescription(description);
            wrapper.setPaymentType(p.getTypeOfPayment());
            wrapper.setSubType(p.getSubType());

            ContractPayment cp = p.getContractPayment();
            if (cp != null) {
                wrapper.setAffectedByAdditionalDiscount(cp.getAdditionalDiscountAmount() != null &&
                        cp.getAdditionalDiscountAmount() > 0.0);
                wrapper.setMoreAdditionalDiscount(cp.getMoreAdditionalDiscount());
            }

            toDo.getContractPaymentList().add(wrapper);

            Setup.getApplicationContext()
                    .getBean(ContractPaymentConfirmationToDoService.class)
                    .createConfirmationToDo(toDo);
            return contractPaymentConfirmationToDoRepository.findOne(toDo.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean isPaymentReceivedByCountPaymentsAndRefunds(
            Contract contract, Double amount,
            PicklistItem type, LocalDate date) {

        int refundsCount = paymentRepository.countRefundsByContractAndPaymentTypeAndDate(
                contract.getId(), amount,
                date.dayOfMonth().withMinimumValue().toDate(),
                date.dayOfMonth().withMaximumValue().toDate(),
                "refund_for:" + type.getCode());

        int paymentsCount = paymentRepository.countReceivedPaymentsByContractAndTypeAndDate(
                contract.getId(), amount,
                date.dayOfMonth().withMinimumValue().toDate(),
                date.dayOfMonth().withMaximumValue().toDate(),
                type.getCode());

        logger.info("contract id: " + contract.getId() +
                "; amount: " + amount +
                "; type: " + type.getCode() +
                "; date: " + date.toString("yyyy-MM-dd") +
                "; refundsCount: " + refundsCount +
                "; paymentsCount: " + paymentsCount);

        return paymentsCount > refundsCount;
    }

    // ACC-6941
    public void disableOnlineCardPaymentLinksOnPaymentReceived(Long paymentId) {
        Payment p = paymentRepository.findOne(paymentId);
        logger.info("payment id: " + paymentId);

        if (!isPaymentReceivedByCountPaymentsAndRefunds(
                p.getContract(), p.getAmountOfPayment(),
                p.getTypeOfPayment(), new LocalDate(p.getDateOfPayment()))) return;

        LocalDate currentPaymentDate = DateUtil.getStartDayPaymentDateOfMonth(p.getContract(), p.getDateOfPayment());
        List<ContractPaymentConfirmationToDo> l = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                        .findOnlineCardTodosByPayment(
                                p.getContract().getId(), p.getTypeOfPayment().getId(),
                                currentPaymentDate.toDate(),
                                currentPaymentDate.plusMonths(1).minusDays(1).toDate());

        Setup.getApplicationContext()
                .getBean(DisableAccountingNotificationService.class)
                .disableOnlineCardPaymentForApprovalOnPaymentStatusChanged(l);
    }

    public boolean existsRunningBouncedPaymentFlow(Contract c) {

        return paymentRepository.existsByContractAndStatusAndReplaced(c, PaymentStatus.BOUNCED, false);
    }

    // ACC-7050
    public void addWaivedPayment(Contract contract) {
        logger.info("Contract id: " + contract.getId() + "; waivedMonths: " + contract.getWaivedMonths());
        DateTime lastPayment = getLastReceivedMonthlyPaymentDate(contract);
        if (lastPayment != null) return;

        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        ContractPaymentType monthlyPaymentType = cpt.getContractPaymentTypes().stream()
                .filter(c -> c.getType().getCode().equals("monthly_payment"))
                .findFirst().orElse(null);
        int waivedMonths = contract.getWaivedMonths();
        LocalDate d = new LocalDate(contract.getStartOfContract());
        while (waivedMonths > 0) {

            addWaivedPayment(d.toDate(), monthlyPaymentType, cpt, monthlyPayment);

            waivedMonths--;
            d = d.plusMonths(1).dayOfMonth().withMinimumValue();
        }
    }

    public void addWaivedPayment(
            Date d, ContractPaymentType monthlyPaymentType, ContractPaymentTerm cpt,
            PicklistItem monthlyPayment) {

        addWaivedPayment(
                d, monthlyPaymentType,
                cpt, monthlyPayment, new HashMap<>());
    }

    public void addWaivedPayment(
            Date d, ContractPaymentType monthlyPaymentType,
            ContractPaymentTerm cpt, PicklistItem monthlyPayment, Map<String, Object> m) {

        if (paymentRepository.existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                cpt.getContract(), PaymentStatus.RECEIVED, monthlyPayment.getCode(), d, cpt.getContract().isOneMonthAgreement() ?
                        new LocalDate(d).plusMonths(1).minusDays(1).toDate() :
                        new LocalDate(d).dayOfMonth().withMaximumValue().toDate())) return;

        Payment p = new Payment();
        p.setContract(cpt.getContract());
        p.setTypeOfPayment(monthlyPayment);
        p.setAmountOfPayment(0.0D);
        p.setVatPaidByClient(false);
        p.setIsProRated(false);
        p.setIncludeWorkerSalary(false);
        p.setStatus(PaymentStatus.RECEIVED);
        p.setNote("Waived Payment");
        p.setManuallyCreated(false);
        p.setOnline(false);
        p.setDateOfPayment(java.sql.Date.valueOf(new LocalDate(d).toString("yyyy-MM-dd")));
        boolean isInitial = calculateDiscountsWithVatService.isDuringPremiumPeriod(cpt, d);
        p.setIsInitial(isInitial);
        p.setMethodOfPayment((PaymentMethod) m.getOrDefault("methodOfPayment", PaymentMethod.CARD));

        ContractPayment cp = new ContractPayment();
        cp.setDate(d);
        cp.setDescription(monthlyPaymentType.getDescription());
        cp.setPaymentType(monthlyPaymentType.getType());
        cp.setContractPaymentTerm(cpt);
        cp.setAmount(0.0D);
        cp.setAdditionalDiscountAmount(0.0D);
        cp.setIncludeWorkerSalary(false);
        cp.setPaymentMethod((PaymentMethod) m.getOrDefault("methodOfPayment", PaymentMethod.CARD));
        cp.setWaived(true);
        cp.setPaymentType(monthlyPayment);
        cp.setIsInitial(isInitial);
        contractPaymentRepository.save(cp);

        forceCreatePayment(p);
    }

    public ContractPayment getContractPayment(Payment p) {

        List<ContractPayment> l = p.getDirectDebitId() != null ?
                contractPaymentRepository.findMatchedContractPaymentViaDd(
                        p.getContract(), p.getDirectDebitId(), p.getMethodOfPayment(), p.getTypeOfPayment(),
                        DateUtil.getDayStart(p.getDateOfPayment()),
                        DateUtil.getDayEnd(p.getDateOfPayment()),
                        p.getAmountOfPayment()) :
                contractPaymentRepository.findMatchedContractPaymentViaWrapper(p.getId());

        ContractPayment cp = l.isEmpty() ? p.getContractPayment() : l.get(0);
        logger.info("Payment Id: " + p.getId() + "; cp id: " + (cp == null ? "NULL" : cp.getId()));

        return cp;
    }

    public void addNewAttachmentToTransaction(Payment p, Attachment a) {
        if (p.getId() == null) return;
        logger.info("Attachment id: " + a.getId() + "; payment id: " + p.getId());

        Transaction transaction = transactionRepository.findFirstByPaymentId(p.getId());
        if (transaction == null) return;

        if (transaction.getAttachment(a.getTag()) != null) {
            logger.info("Attachment with tag: " + a.getTag() + " is already exist on transaction");
            return;
        }

        Attachment copied = Storage.cloneTemporary(a, "tr_" + a.getTag());

        logger.log(Level.SEVERE, "Attachment entity copy: " + copied.getId());
        List<Attachment> attachments = transaction.getAttachments();
        attachments.add(copied);
        transaction.setAttachments(attachments);

        if (a.getTag().toUpperCase().startsWith("VAT_")
                || a.getTag().toUpperCase().startsWith("PAYMENT_TAX_INVOICE")
                || a.getTag().toUpperCase().startsWith("PAYMENT_TAX_CREDIT_NOTE")
                || a.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_INVOICE")
                || a.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_CREDIT_NOTE")) {

            transaction.setMissingTaxInvoice(false);
        }

        transactionRepository.save(transaction);
    }

    // ACC-6103 TaxInvoice + TaxCreditNote
    public void addAttachmentOnPayment(Long paymentId, Long attachmentId) {
        Payment p = paymentRepository.findOne(paymentId);
        Attachment a = Setup.getRepository(AttachementRepository.class).findOne(attachmentId);
        if (p == null || a == null) return;
        logger.info("payment id: " + paymentId + "; attachment id: " + attachmentId);

        p.addAttachment(a);
        updatePaymentSilent(p);
    }

    public void generateRecurringPayment(ContractPaymentWrapper w, LocalDate expiryDate, ContractPaymentTerm cpt) {
        logger.info("w id: " + w.getId());

        LocalDate startDate = cpt.getContract().isOneMonthAgreement() ?
                oneMonthAgreementFlowService.getPaymentDate(cpt.getContract(), new DateTime(w.getPaymentDate())).plusMonths(1).toLocalDate() :
                new LocalDate(w.getPaymentDate()).plusMonths(1).dayOfMonth().withMinimumValue();

        generateRecurringPayment(
                cpt, startDate, expiryDate, w.getPaymentType(),
                w.getContractPayment() != null && w.getContractPayment().getSubType() != null ? w.getContractPayment().getSubType() : null);
    }

    public void generateRecurringPayment(ContractPaymentTerm cpt, LocalDate startDate, LocalDate expiryDate, PicklistItem paymentType, PicklistItem subType) {
        logger.info("cpt id: " + cpt.getId() +
                " ;expiryDate: " + expiryDate.toString("yyyy-MM-dd"));

        logger.info("startDate: " + startDate.toString("yyyy-MM-dd"));
        if (expiryDate.isBefore(startDate)) return;

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        // variable to check whether payment added.
        boolean paymentGenerated = false;

        while (startDate.isBefore(expiryDate)) {
            LocalDate paymentDate = startDate;
            startDate = startDate.plusMonths(1);

            if(calculateDiscountsWithVatService.isDuringPremiumPeriod(cpt, paymentDate.toDate())){
                logger.info("payment date: " + paymentDate.toString("yyyy-MM-dd") + "; isDuringPremiumPeriod");
                continue;
            }

            if (paymentDate.toDate().getTime() <= cpt.getContract().getPaidEndDate().getTime()) {
                logger.info("payment date: " + paymentDate.toString("yyyy-MM-dd") + "; before PED");
                continue;
            }

            Map<String, Object> result = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, paymentDate, true);
            if((result.get("moreAdditionalDiscount") != null &&
                        (Double) result.get("moreAdditionalDiscount") != 0.0) ||
                    (result.get("additionalDiscountAmountPerPayment") != null &&
                        (Double) result.get("additionalDiscountAmountPerPayment") !=  0.0) || // OMA
                    (result.get("additionalDiscount") != null &&
                        (Double) result.get("additionalDiscount") !=  0.0)){
                logger.info("payment date: " + paymentDate.toString("yyyy-MM-dd") + "; has additional discount");
                continue;
            }

            if (DiscountsWithVatHelper.getAddOnPaymentType(paymentDate.toDate(), cpt.getContractPaymentTypes(), cpt.getContract()) != null) {
                logger.info("payment date: " + paymentDate.toString("yyyy-MM-dd") + "; has add-on");
                continue;
            }

            if(contractPaymentConfirmationToDoRepository.existsPaymentInInitialFlow(
                    cpt.getContract(),
                    paymentDate.toDate(),
                    paymentDate.plusMonths(1).toDate())) {
                logger.info("payment date: " + paymentDate.toString("yyyy-MM-dd") + "; cover by initial flow");
                continue;
            }

            if(!cpt.getSourceAmount().equals(result.get("amount"))) {
                logger.info("Source Amount: " + cpt.getSourceAmount() + "; recurring Payment amount" + result.get("amount") +
                        "; different amount");
                continue;
            }

            Payment p = new Payment();
            paymentGenerated = true; // payment added set to true.
            p.setContract(cpt.getContract());
            p.setTypeOfPayment(paymentType);
            p.setDateOfPayment(java.sql.Date.valueOf(paymentDate.toString("yyyy-MM-dd")));
            p.setAmountOfPayment((Double) result.get("amount"));
            p.setRecurring(true);
            p.setStatus(PaymentStatus.PDC);
            p.setOnline(true);
            p.setMethodOfPayment(PaymentMethod.CARD);
            p.setVatPaidByClient(true);
            p.setIsInitial(false);
            p.setIsProRated(false);
            p.setIncludeWorkerSalary(cpt.getContract().isMaidVisa());
            p.setAutomaticallyAdded(true);
            p.setDiscount(cpt.getDiscount());

            Double workerSalary = cpt.getContract().getWorkerSalaryNew();
            if (workerSalary != null && workerSalary > 0) {
                p.setWorkerSalary(workerSalary);
            }

            if (subType != null) {
                p.setSubType(subType);
            }

            forceCreatePayment(p);
        }
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);
        if (paymentGenerated) {
            // update paid end date when one or more payments generated.
            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .updatePaidEndDate(cpt.getContract());
        }
    }

    public void removeRecurringPayment(ContractPaymentTerm cpt)  {
        logger.info("cpt id: " + cpt.getId());
        List<Payment> payments = paymentRepository.findByContractAndRecurringTrue(cpt.getContract());

        if (payments.isEmpty()) return;

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);
        payments.forEach(p -> {
            try {
                p.setStatus(PaymentStatus.DELETED);
                updatePaymentSilent(p);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaidEndDate(cpt.getContract());
    }

    public boolean nextMonthRecurringAndMatchTokenAmount(ContractPaymentTerm cpt) {
        LocalDate paidEndDate = new LocalDate(cpt.getContract().getPaidEndDate());
        logger.info("cpt id: " + cpt.getId() + "; PED: " + paidEndDate.toString("yyyy-MM-dd"));

        boolean nextMonthRecurringAndMatchTokenAmount = paymentRepository.existsByMonthAndContractAndRecurringTrue(
                cpt.getContract(),
                paidEndDate.plusDays(1).toDate(),
                paidEndDate.plusMonths(1).toDate(),
                cpt.getSourceAmount());
        logger.info("nextMonthRecurringAndMatchTokenAmount: " + nextMonthRecurringAndMatchTokenAmount);

        return nextMonthRecurringAndMatchTokenAmount;
    }

    public boolean changeRecurringPaymentToReceived(ContractPaymentWrapper w) {
        if (!PaymentHelper.isMonthlyPayment(w.getPaymentType())) return false;

        List<Payment> l = paymentRepository.findRecurringPaymentByContractAndPaymentDate(
                w.getContractPaymentConfirmationToDo().getContractPaymentTerm().getContract().getId(),
                w.getContractPaymentConfirmationToDo().getContractPaymentList().get(0).getPaymentDate(),
                new LocalDate(w.getContractPaymentConfirmationToDo().getContractPaymentList().get(0).getPaymentDate()).plusMonths(1).minusDays(1).toDate(),
                w.getAmount());

        if (l.isEmpty()) {
            return false;
        }

        Payment p = l.get(0);
        flowProcessorService.stopRecurringFailureFlows(p.getContract(), w.getContractPaymentConfirmationToDo(), true);

        p.setStatus(PaymentStatus.RECEIVED);
        forceUpdatePayment(p);

        w.setGeneratedPaymentId(l.get(0).getId());
        contractPaymentWrapperRepository.save(w);

        return true;
    }

    public boolean checkIfPaymentMatchesWithPTCAndUpdatePaymentsDateIfNeeded(String bodyJson) throws JsonProcessingException {
        ObjectMapper objectMapper = Setup.getApplicationContext()
                .getBean(ObjectMapperConfiguration.class)
                .objectMapper();
        return checkIfPaymentMatchesWithPTCAndUpdatePaymentsDateIfNeeded(objectMapper.readValue(bodyJson, HashMap.class), objectMapper);
    }

    public boolean checkIfPaymentMatchesWithPTCAndUpdatePaymentsDateIfNeeded(Map<String, Object> body, ObjectMapper objectMapper) {

        try {
            logger.info("requestBody for API : '/payments/checkIfPaymentMatchesWithPTC': " +  new ObjectMapper().writeValueAsString(body));

            // 1- Check if we should be Update Payments Date
            if (body.containsKey("updatePaymentDatesByIds")) {
                DateTime contractStartDate = body.get("contractStartDate") instanceof Long ?
                        new DateTime(body.get("contractStartDate")).withTimeAtStartOfDay() :
                        DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(body.get("contractStartDate").toString()).withTimeAtStartOfDay();

                Setup.getApplicationContext()
                        .getBean(PaymentService.class)
                        .updateDateOfPayments(
                                Arrays.asList(((String) body.get("updatePaymentDatesByIds")).split(",")),
                                contractStartDate);
            }

            // 2- Mapping paymentTypeConfigs
            List<AbstractPaymentTypeConfig> paymentTypeConfigs = ((List<Object>) body.get("paymentTypeConfigs")).stream()
                    .map(o -> objectMapper.convertValue(o, PaymentTypeConfig.class)).collect(Collectors.toList());

            PaymentTermConfig paymentTermConfig = objectMapper.convertValue(body.get("paymentTermConfig"), PaymentTermConfig.class);

            body.put("dailyRateAmount", paymentTermConfig.getDailyRateAmount());
            body.put("paymentTypeConfigs", paymentTypeConfigs);
            body.put("abstractPaymentTerm", paymentTermConfig);

            ContractPaymentService contractPaymentService = Setup.getApplicationContext().getBean(ContractPaymentService.class);
            Map<String, Boolean> matchingDDcWithCPT = contractPaymentService.getParameterFeatureMatchingDDcWithCPT();
            if (!matchingDDcWithCPT.get("matchWithDDs") && !matchingDDcWithCPT.get("matchWithPayments")) {
                return true;
            }

            // 3- ACC-7321 check any active MONTHLY DD that doesn’t match
            List<ContractPayment> contractPayments;
            if (matchingDDcWithCPT.get("matchWithDDs")) {
                contractPayments = Setup.getRepository(ContractPaymentRepository.class)
                        .findAllContractPaymentOfActiveMonthlyDdByContract(
                                Long.valueOf((Integer) body.get("contractId")),
                                DirectDebitService.notAllowedStatusesWithRejected);

                logger.info("Contract Payments of Monthly DD size: " + contractPayments.size());

                body.put("contractPayments", contractPayments);

                Map<String, Object> result = contractPaymentService.checkIfPaymentMatchesWithPTC(body);
                if (!(Boolean) result.get("matched")) {
                    DirectDebit dd = ((ContractPayment) result.get("contractPaymentNotMatched")).getDirectDebit();
                    throw new BusinessException("There’s an active DD with start date: " + new LocalDate(dd.getStartDate()).toString("yyyy-MM-dd") +
                            " and amount: " + dd.getAmount() + " that doesn’t match the selected payment term, " +
                            "please either cancel the DD or change the selected payment term");
                }
            }

            // 4- ACC-6993 check any payment received that doesn’t match
            if (!matchingDDcWithCPT.get("matchWithPayments")) {
                return true;
            }

            List<Long> paymentIds = paymentRepository.findAllPaymentReceivedByContract(Long.valueOf((Integer) body.get("contractId")))
                    .stream().map(o -> Long.valueOf(o[0].toString())).collect(Collectors.toList());
            logger.info("Payments size: " + paymentIds.size());

            contractPayments = paymentRepository.findAll(paymentIds)
                    .stream()
                    .map(p -> {
                        ContractPayment cp = p.getContractPayment();
                        logger.info("Payment Id: " + p.getId() + "; cp id: " + (cp == null ? "NULL" : cp.getId()));
                        return cp;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            body.put("contractPayments", contractPayments);
            return (boolean) contractPaymentService.checkIfPaymentMatchesWithPTC(body).get("matched");
        } catch (BusinessException businessException) {
            throw businessException;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("Exception : previous Payment doesn't have contract payment => return false");
            return false;
        }
    }

    // ACC-8592
    @Transactional
    public void updateDateOfPayments(List<String> paymentIdsAsString, DateTime date) throws Exception {

        logger.info("paymentIds size: " + paymentIdsAsString.size() + "date: " + date);
        if (paymentIdsAsString.isEmpty() || date == null) return;
        List<Long> paymentIds = paymentIdsAsString.stream().map(Long::valueOf).collect(Collectors.toList());
        Date paymentDate = date.toDate();

        // 1- Stop update Paid end date
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        // 2- Update Date of Payment
        List<Payment> payments = paymentRepository.findAll(paymentIds);
        if (payments.isEmpty()) return;

        payments.forEach(p -> {
            logger.info("Payment Id : " + p.getId());
            p.setDateOfPayment(new java.sql.Date(paymentDate.getTime()));
            paymentRepository.silentSave(p);
        });

        // 3- Start update Paid end date
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);

        // 4- Update Paid end date Manual
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaidEndDate(payments.get(0).getContract());

        // 5- Update Date of Wrapper and Contract Payment
        List<ContractPaymentWrapper> wrappers = contractPaymentWrapperRepository.findByContractPayment_GeneratedPaymentIdIn(paymentIds);
        wrappers.forEach(w -> {
            logger.info("Wrapper Id : " + w.getId() +
                    "; Contract Payment Id: " + (w.getContractPayment() != null ? w.getContractPayment().getId() : "NULL"));
            w.setPaymentDate(paymentDate);
            contractPaymentWrapperRepository.save(w);

            if (w.getContractPayment() == null) return;
            w.getContractPayment().setDate(new java.sql.Date(paymentDate.getTime()));
            contractPaymentRepository.save(w.getContractPayment());
        });
    }

    public Map<String, Object> getPaymentsReportGPT(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName) {
        List<Housemaid> housemaids = housemaidService.getHousemaids(mobileNumber, eidNumber, firstName, middleName, lastName, null);
        Map<String, Object> results = new HashMap<>();
        logger.info("housemaid name : " + firstName + " - " + middleName + " - " + lastName +
                ", mobileNumber : " + mobileNumber +
                ", eidNumber : " + eidNumber);

        if (housemaids.isEmpty()) {
            results.put("no_maids", true);
            return results;
        } else if (housemaids.size() > 1) {
            results.put("multiple_maids", true);
        }
        //In case of multiple maids : filter maids on ET , if there isn't any maid left ? get first ET maid : get first non ET maid
        List<Housemaid> nonETMaids = housemaids.stream()
            .filter(h -> !HousemaidStatus.EMPLOYEMENT_TERMINATED.equals(h.getStatus()))
            .collect(Collectors.toList());

        Contract contract = contractRepository.findFirstOneByHousemaidOrderByCreationDateDesc(
                nonETMaids.isEmpty() ? housemaids.get(0) : nonETMaids.get(0));

        if (contract == null) return results;

        logger.info("contract id : " + contract.getId().toString());
        StringBuilder clientPaymentReport = new StringBuilder();

        String minPaymentsDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MIN_DATE_PAYMENTS_VALUE);
        List<Object[]> payments = paymentRepository.findByContractAndCreationDate(contract, new LocalDate(minPaymentsDate).toDate());
        for (int i = 0; i < payments.size(); i++) {
            Object[] row = payments.get(i);
            if (i == 0 ) {
                clientPaymentReport.append("Client made payment ");
            }
            if (i > 0) {
                clientPaymentReport.append(", then made payment ");
            }
            PaymentStatus status = (PaymentStatus) row[1];
            clientPaymentReport.append(row[0])
                    .append(" of status ").append(status.getLabel()).append(PaymentStatus.BOUNCED.equals(status) ? " (" + ((boolean) row[5] ? "replaced" : "non replaced") + ")" : "")
                    .append(" and method ").append(((PaymentMethod) row[2]).getLabel())
                    .append(" and type ").append(row[3])
                    .append(" on ").append(new LocalDate(row[4]).toString("yyyy-MM-dd"));

            if (status.equals(PaymentStatus.RECEIVED)) {
                clientPaymentReport.append(" which was received on ").append(new LocalDate(row[6]).toString("yyyy-MM-dd"));
            }
        }
        results.put("clientPaymentReport", clientPaymentReport);

        return results;
    }

    public Map<String, Object> doFullRefund(Contract contract, Client client, Payment payment, Double amount) {
        Map<String, Object> map = new HashMap<>();
        PaymentHelper.doFullRefund(contract, client, payment, amount, false);
        map.put("refundSentToExpensify", payment.getRefundSentToExpensify());
        map.put("fullyRefunded", payment.getFullyRefunded());
        map.put("clientInformedAboutRefund", payment.getClientInformedAboutRefund());
        return map;
    }

    public Map<String, Object> calculateTotalCreditNoteAmount(Contract contract) {
        Map<String, Object> m = new HashMap<>();
        m.put("creditNoteAmount", 0D);
        if (contract == null || contract.getHousemaid() == null ||
                contract.getHousemaid().getNationality() == null) return m;

        List<Map<String, Object>> payments = paymentRepository.findAllPaymentsReceivedByContractAndTypes(
                contract, Arrays.asList(
                        AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE,
                        AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                        AbstractPaymentTypeConfig.PRE_COLLECTED_PAYMENT_CODE,
                        AbstractPaymentTypeConfig.PRE_COLLECTED_PAYMENT_NO_VAT_CODE));


        double totalAmountOfMonthlyPayments = 0, totalAmountOfSDR = 0, totalAmountOfPreCollected = 0;
        int numberOfMonths = 0;
        for (Map<String, Object> payment : payments) {
            switch ((String) payment.get("paymentTypeCode")) {
                case AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE:
                    totalAmountOfSDR += (double) payment.get("amount");
                break;
                case AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE:
                    totalAmountOfMonthlyPayments += (double) payment.get("amount");
                    numberOfMonths++;
                    break;
                case AbstractPaymentTypeConfig.PRE_COLLECTED_PAYMENT_CODE:
                case AbstractPaymentTypeConfig.PRE_COLLECTED_PAYMENT_NO_VAT_CODE:
                    totalAmountOfPreCollected += (double) payment.get("amount");
                    break;
            }
        }

        boolean isFilipinoMaid = contract.getHousemaid().getNationality().getCode()
                .equals(AccountingModule.PICKLIST_ITEM_NATIONALITY_FILIPINO_1);

        double defaultAmount = Utils.parseValue(
                Setup.getParameter(Setup.getCurrentModule(), isFilipinoMaid ?
                        AccountingModule.PARAMETER_DEFAULT_CC_AMOUNT_FOR_FILIPINA :
                        AccountingModule.PARAMETER_DEFAULT_CC_AMOUNT_FOR_NON_FILIPINA),
                Double.class);

        double oldPrice = numberOfMonths * defaultAmount;
        double creditNoteAmount = totalAmountOfSDR + totalAmountOfMonthlyPayments + totalAmountOfPreCollected - oldPrice;

        m.put("oldPrice", oldPrice);
        m.put("totalAmountOfSDR", totalAmountOfSDR);
        m.put("totalAmountOfMonthlyPayments", totalAmountOfMonthlyPayments);
        m.put("totalAmountOfPreCollected", totalAmountOfPreCollected);
        m.put("creditNoteAmount", Math.max(creditNoteAmount, 0.0D));
        return m;
    }
}