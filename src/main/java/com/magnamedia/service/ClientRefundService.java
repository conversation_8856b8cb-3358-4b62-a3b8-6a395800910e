package com.magnamedia.service;

import com.google.api.client.util.ArrayMap;
import com.magnamedia.controller.ClientRefundTodoController;
import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.epayment.EPaymentTransaction;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.epayment.EPaymentProvider;
import com.magnamedia.core.helper.epayment.EPaymentService;
import com.magnamedia.core.helper.epayment.ETransactionStatus;
import com.magnamedia.core.helper.epayment.providers.RefunRequest;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.repository.epayment.EPaymentTransactionRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.extra.*;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ketrawi
 * Acc-2826
 */

@Service
public class ClientRefundService {
    protected static final Logger logger = Logger.getLogger(ClientRefundService.class.getName());

    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ClientRefundTodoController clientRefundTodoCtrl;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private PaymentRequestPurposeRepository paymentRequestPurposeRepository;

    // Refund amount on a monthly basis
    public void createChildTodo(ClientRefundToDo clientRefundToDo, double amount) {

        ClientRefundToDo childTodo = new ClientRefundToDo();
        childTodo.setAutomaticRefund(true);
        childTodo.setFlowTriggered("Add accountant todo for pending client refund");
        childTodo.setContract(clientRefundToDo.getContract());
        childTodo.setClient(clientRefundToDo.getClient());
        childTodo.setPurpose(clientRefundToDo.getPurpose());
        childTodo.setAmount(amount);
        childTodo.setNumberOfMonthlyPayments(null);
        childTodo.setComplaint(clientRefundToDo.getComplaint());
        childTodo.setMethodOfPayment(clientRefundToDo.getMethodOfPayment());
        childTodo.setStatus(ClientRefundStatus.PENDING);
        childTodo.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));
        childTodo.setTransferReference(clientRefundToDo.getTransferReference());

        if (!clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
            childTodo.setManagerAction(clientRefundToDo.getManagerAction());
            childTodo.setManagerActionBy(clientRefundToDo.getManagerActionBy());
            childTodo.setCeoAction(clientRefundToDo.getCeoAction());
            childTodo.setCeoActionBy(clientRefundToDo.getCeoActionBy());
        }

        childTodo.setAccountName(clientRefundToDo.getAccountName());
        childTodo.setIban(clientRefundToDo.getIban());
        childTodo.setEid(clientRefundToDo.getEid());

        childTodo.setDescription(clientRefundToDo.getDescription());
        childTodo.setNotes(clientRefundToDo.getNotes());

        childTodo.setRequestType(clientRefundToDo.getRequestType());
        childTodo.setNumberOfUnusedDays(clientRefundToDo.getNumberOfUnusedDays());
        childTodo.setNumberOfUsedDays(clientRefundToDo.getNumberOfUsedDays());
        childTodo.setPartialRefundForCancellationPaymentMethod(clientRefundToDo.getPartialRefundForCancellationPaymentMethod());

        childTodo.setTaskName(clientRefundToDo.getTaskName());
        childTodo.setParent(clientRefundToDo);

        clientRefundTodoCtrl.createEntity(childTodo);
    }

    public Map<String, Object> getRefundData(
            Contract contract,
            Date targetDate,
            PaymentRequestPurpose purpose,
            Boolean partialRefund) {

        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();

        PicklistItem monthlyPaymentType = Setup.getItem("TypeOfPayment", "monthly_payment");
        Payment monthlyReceivedNotProratedPayment = paymentRepository
                .findFirstByContractAndStatusAndTypeOfPaymentAndIsProRatedOrderByDateOfPaymentDesc
                        (contract, PaymentStatus.RECEIVED, monthlyPaymentType, false);

        Map<String, Object> response = new HashMap<>();
        response.put("cptAmount", Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getAmountOfMonthlyPaymentAtTimeWithDiscounts(activeCPT, new LocalDate(targetDate)).get("amount"));

        Double receivedPayment;
        if (monthlyReceivedNotProratedPayment != null) {
            receivedPayment = monthlyReceivedNotProratedPayment.getAmountOfPayment();
        } else {
            Payment monthlyReceivedProratedPayment = paymentRepository
                    .findFirstByContractAndStatusAndTypeOfPaymentAndIsProRatedOrderByDateOfPaymentDesc
                            (contract, PaymentStatus.RECEIVED, monthlyPaymentType, true);

            if (monthlyReceivedProratedPayment != null) {
                receivedPayment = activeCPT.getMonthlyPayment();
            } else {
                receivedPayment = 0.0;
            }
        }
        response.put("receivedPaymentAmount", receivedPayment);

        Boolean isScheduledForTermination = false;
        Boolean isTerminated = false;
        DateTime terminationDate = null;
        if (contract.getDateOfTermination() != null) {
            terminationDate = new DateTime(contract.getDateOfTermination()).withTimeAtStartOfDay();
            isScheduledForTermination = true;
            isTerminated = true;
        } else if (contract.getScheduledDateOfTermination() != null) {
            terminationDate = new DateTime(contract.getScheduledDateOfTermination()).withTimeAtStartOfDay();
            isScheduledForTermination = true;
        }

        //ACC-4738
        Date dateOfReleased = null;
        if (contract.getHousemaid() == null) {
            HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
            query.filterBy("id", "=", contract.getId());
            query.filterBy("housemaid", "is null", null);
            query.filterByChanged("housemaid");
            query.sortBy("lastModificationDate", false);

            dateOfReleased = query.execute().get(0).getLastModificationDate();
        } else if(terminationDate != null && isTerminated) {
            dateOfReleased = terminationDate.toDate();
        } else {
            dateOfReleased = new Date();
        }

        long unusedDays = 0;

        if((partialRefund ||
                (purpose != null &&
                        purpose.getName().equals("Partial Refunds for Cancellation"))) &&
                terminationDate != null) {

            unusedDays = getDuration(terminationDate.toDate(), contract.getPaidEndDate());

        } else if(purpose != null && purpose.getName().toLowerCase().startsWith(
                "compensating clients for bad experience/days without service")) {

            if (contract.getHousemaid() == null) {
                unusedDays = getDuration(dateOfReleased, new Date());
            } else {
                HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
                query.filterBy("id", "=", contract.getId());
                query.filterByChanged("housemaid");
                query.sortBy("lastModificationDate", false);
                List<Contract> history = query.execute();

                Contract unassigned = history.stream()
                        .filter(c -> c.getHousemaid() == null)
                        .findFirst().orElse(null);
                if(unassigned != null) {
                    Date dateUnassigned = unassigned.getLastModificationDate();
                    Date dateAssigned = null;

                    for(Contract c : history) {
                        if(c.getHousemaid() != null) {
                            if(c.getLastModificationDate().getTime() < dateUnassigned.getTime()) {
                                break;
                            }
                            dateAssigned = c.getLastModificationDate();
                        }
                    }

                    unusedDays = getDuration(dateUnassigned,
                            dateAssigned == null ? new Date() : dateAssigned);
                }
            }
        }

        //Payment of termination month
        Double ministryRate = Double.parseDouble(Setup.getParameter(
                Setup.getModule("clientmgmt"), "MINISTRY_RATE_FOR_PARTIAL_REFUND"));
        List<Payment> paymentsOfTerminationMonth = paymentRepository.getPaymentAmountByYearAndMonth
                (contract, new LocalDate(terminationDate).getYear(),
                        new LocalDate(terminationDate).getMonthOfYear());

        Double maximumAllowedRefund = receivedPayment - ministryRate;
        if (!paymentsOfTerminationMonth.isEmpty()) {
            Double paymentOfTerminationamount = paymentsOfTerminationMonth.get(0).getAmountOfPayment();
            maximumAllowedRefund = paymentOfTerminationamount - ministryRate;

            response.put("paymentOfTerminationMonth", paymentOfTerminationamount);
        }

        int usedDays = Math.max(0, dateOfReleased == null ? 0 :
                1 + DateUtil.getDaysBetween(
                        new DateTime().withTimeAtStartOfDay().withDayOfMonth(1).toDate(),
                        dateOfReleased));
        long usedWeeks = usedDays % 7 == 0 ? usedDays / 7 : usedDays / 7 + 1;

        response.put("usedDays", usedDays);
        response.put("usedWeeks", Math.min(usedWeeks, 4)); // ACC-5429
        response.put("unusedDays", Math.max(unusedDays, 0));
        response.put("maximumAllowedRefund", maximumAllowedRefund);
        response.put("isScheduledForTermination", isScheduledForTermination);
        response.put("isTerminated", isTerminated);
        response.put("isWeeklyPlan", activeCPT.getWeeklyAmount() > 0.0);

        return response;
    }

    public long getDuration(Date d1, Date d2) {
        LocalDate ld1 = LocalDate.fromDateFields(d1);
        LocalDate ld2 = LocalDate.fromDateFields(d2);
        return Days.daysBetween(ld1, ld2).getDays();
    }

    // ACC-5663
    public void sendNotificationToClient(ClientRefundToDo todo) {
        Contract contract = contractRepository.findOne(todo.getContract().getId());
        logger.log(Level.INFO, "todo id: {0}", todo.getId());

        Map<String, String> parameters = new HashMap<>();
        Map<String, AppAction> context = new HashMap<>();
        String templateName = null;

        switch (todo.getStatus()) {
            case PENDING:
                parameters.put("amount", String.valueOf(todo.getAmount().intValue()));
                if (todo.isConditionalRefund()) {
                    if (todo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                        templateName = contract.isMaidCc() ?
                                CcNotificationTemplateCode.CC_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString() :
                                MvNotificationTemplateCode.MV_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString();
                    } else {
                        templateName = contract.isMaidCc() ?
                                CcNotificationTemplateCode.CC_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString() :
                                MvNotificationTemplateCode.MV_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString();
                    }
                    parameters.put("payments_amount", getConditionalRefundAmountAndDatePayment(todo));

                } else {
                    if (todo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                        templateName = contract.isMaidCc() ?
                                CcNotificationTemplateCode.CC_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString() :
                                MvNotificationTemplateCode.MV_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString();
                    } else {
                        templateName = contract.isMaidCc() ?
                                CcNotificationTemplateCode.CC_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString() :
                                MvNotificationTemplateCode.MV_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString();
                    }
                }
                break;
            case REJECTED:
                parameters.put("greetings", Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC));

                List<PushNotification> l = pushNotificationHelper.getByOwnerTypeAndId("ClientRefundToDo", todo.getId());
                if (!l.isEmpty()) {
                    pushNotificationHelper.stopDisplaying(l);
                }

                String normalizedWANumber = StringHelper.NormalizePhoneNumber(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MAIDS_CC_CLIENT_CALL));
                String link = "https://wa.me/" + normalizedWANumber;
                parameters.put("whatsApp_us_sms", Setup.getApplicationContext()
                        .getBean(Utils.class)
                        .shorteningUrl(link));
                AppAction chatWithUs = new AppAction();
                chatWithUs.setType(AppActionType.LINK);
                chatWithUs.setText("Whatsapp us");
                chatWithUs.setFunctionType(FunctionType.WEB_SERVICE);
                chatWithUs.setNavigationType(NavigationType.WEB);
                chatWithUs.setHyperlink(parameters.get("whatsApp_us_sms"));
                chatWithUs.setAppRouteName("");
                chatWithUs.setAppRouteArguments(new HashMap<>());

                context.put("whatsApp_us", chatWithUs);
                parameters.put("whatsApp_us", "@whatsApp_us@");
                if (todo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                    templateName = contract.isMaidCc() ?
                            CcNotificationTemplateCode.CC_ACCOUNTING_REFUND_REJECTED_CREDIT_CARD_NOTIFICATION.toString() :
                            MvNotificationTemplateCode.MV_ACCOUNTING_REFUND_REJECTED_CREDIT_CARD_NOTIFICATION.toString();
                } else {
                    templateName = contract.isMaidCc() ?
                            CcNotificationTemplateCode.CC_ACCOUNTING_REFUND_REJECTED_NOTIFICATION.toString() :
                            MvNotificationTemplateCode.MV_ACCOUNTING_REFUND_REJECTED_NOTIFICATION.toString();
                }
                break;
        }

        if (templateName == null) return;
        logger.log(Level.INFO, "templateName id: {0}", templateName);

        Template t = TemplateUtil.getTemplate(templateName);

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendMessageToClient(
                        contract,
                        parameters,
                        context,
                        todo.getId(),
                        "ClientRefundToDo",
                        t);
    }

    // ACC-5663
    public String getConditionalRefundPayment(
            ClientRefundToDo todo) {

        List<Payment> payments = todo.getRequiredPayments();
        if (payments.isEmpty()) return "";

        if (payments.size() > 1) {
            StringBuilder amount = new StringBuilder("payments of AED ");
            for (int i = 1; i <= payments.size(); i++) {
                Payment p = paymentRepository.findOne(payments.get(i - 1).getId());
                amount.append(PaymentHelper.df.format(p.getAmountOfPayment().intValue()));
                if (i < payments.size() - 1)
                    amount.append(", ");
                else if (i == payments.size() - 1)
                    amount.append(", and ");
            }
            return amount.toString();
        } else return "payment of AED " + PaymentHelper.df.format(paymentRepository.findOne(payments.get(0).getId())
                .getAmountOfPayment()
                .intValue());
    }

    // ACC-7611
    private String getConditionalRefundAmountAndDatePayment(ClientRefundToDo todo) {

        List<Payment> payments = todo.getRequiredPayments();
        if (payments.isEmpty()) return "";

        StringBuilder valueOfParameter = new StringBuilder();

        for (int i = 0; i < payments.size(); i++) {
            valueOfParameter
                    .append(getParameterAmountAndDateOfPayment(paymentRepository.findOne(payments.get(i).getId())))
                    .append(i < payments.size() - 1 ? " and " : "");
        }
        return valueOfParameter.toString();
    }

    private String getParameterAmountAndDateOfPayment(Payment p) {
       return "payment of AED " + PaymentHelper.df.format(p.getAmountOfPayment().intValue()) +
               " due on " + new LocalDate(p.getDateOfPayment()).toString("yyyy-MM-dd");
    }

    public Map<String, String> getProofTransferLink(Long toDoId) {
        Map<String, String> params = new HashMap<>();
        params.put("proof_of_transfer_sentence_sms", "");

        List<Attachment> transferProofList = Setup.getRepository(AttachementRepository.class)
                .findByOwnerIdAndOwnerTypeAndTag(
                        toDoId, PayrollAccountantTodo.class.getSimpleName(), "client_refund_transfer_slip");

        if (transferProofList.isEmpty()) return params;

        String transferProofLink = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BACKEND_BASE_URL)
                + "/public/download/" + transferProofList.get(0).getUuid();

        params.put("proof_of_transfer_link", Setup.getApplicationContext()
                .getBean(Shortener.class)
                .shorten(transferProofLink));
        params.put("proof_of_transfer_sentence_sms", " Click here to view the proof of transfer: " + params.get("proof_of_transfer_link"));
        return params;
    }

    public void sendNotification(
            Contract contract,
            String contentTemplate,
            Map<String, String> parameters,
            boolean sendSpouse) {

        contract = Setup.getRepository(ContractRepository.class).findOne(contract.getId());
        Template notificationTemplate = TemplateUtil.getTemplate(contentTemplate);

        if(notificationTemplate == null) return;

        logger.info( "contract id: " + contract.getId() +
                "; Template id: " + notificationTemplate.getId());

        messagingService.sendMessageToClient(
                contract,
                parameters,
                new HashMap<>(),
                contract.getId(),
                contract.getEntityType(),
                notificationTemplate);

        if (!sendSpouse ||
                (contract.getClient().getNormalizedSpouseMobileNumber() == null &&
                        contract.getClient().getNormalizedSpouseWhatsappNumber() == null)) return;

        messagingService.sendClientSms(contract,
                notificationTemplate,
                parameters,
                new HashMap<>(),
                contract.getClient().getNormalizedSpouseMobileNumber(),
                contract.getClient().getNormalizedSpouseWhatsappNumber(),
                contract.getId(),
                contract.getEntityType());
    }

    @Transactional
    public void refundOnlineCreditCardPayment(ClientRefundToDo clientRefundToDo) {
        logger.info("start refund for todo id: " + clientRefundToDo.getId() + "; amount: " + clientRefundToDo.getAmount());
        if (clientRefundToDo.getContractPaymentConfirmationToDo() == null) return;

        Map<String, Object> m = getReceivedTransactionAndTotalAllowedRefund(clientRefundToDo, clientRefundToDo.getTransferReference());
        Double allowRefundAmount = (Double) m.get("allowRefundAmount");
        EPaymentTransaction receivedTransaction = (EPaymentTransaction) m.get("receivedTransaction");

        logger.info("Amount Refund: " + clientRefundToDo.getAmount() +
                "; allowRefundAmount: " + allowRefundAmount);
        if (clientRefundToDo.getAmount() > allowRefundAmount) {
            throw new BusinessException("Selected payment amount is less than the refund amount.");
        }

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "applyRefundOnlineCreditCardPaymentOnProvider_" + clientRefundToDo.getId(),
                        "accounting",
                        "clientRefundService",
                        "applyRefundOnlineCreditCardPaymentOnProvider")
                        .withParameters(
                                new Class[] {Long.class, Long.class},
                                new Object[] {clientRefundToDo.getId(), receivedTransaction.getId()})
                        .withRelatedEntity("ClientRefundToDo", clientRefundToDo.getId())
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public void applyRefundOnlineCreditCardPaymentOnProvider(Long refundToDoId, Long receivedTransactionId) {
        if (QueryService.existsEntity(BackgroundTask.class,
                "e.name = :p0 and e.NumberofRetries > 0",
                new Object[]{"applyRefundOnlineCreditCardPaymentOnProvider_" + refundToDoId})) {
            return;
        }
        logger.info("start refund for todo id: " + refundToDoId + "; receivedTransactionId: " + receivedTransactionId);

        EPaymentTransactionRepository ePaymentTransactionRepository = Setup.getRepository(EPaymentTransactionRepository.class);
        EPaymentTransaction receivedTransaction = ePaymentTransactionRepository.findOne(receivedTransactionId);
        ClientRefundToDo clientRefundToDo = clientRefundTodoRepository.findOne(refundToDoId);

        EPaymentTransaction transactionRefunded = Setup.getApplicationContext()
                .getBean(EPaymentService.class)
                .refund(RefunRequest
                        .RefunRequestBuilder
                        .aRefunRequest()
                        .withTransaction(receivedTransaction)
                        .withAmountToBeRefunded(clientRefundToDo.getAmount())
                        .withRelatedEntityId(clientRefundToDo.getContractPaymentConfirmationToDo().getId())
                        .withRelatedEntityType(clientRefundToDo.getContractPaymentConfirmationToDo().getEntityType())
                        .withCallbackServiceName("accountingEPaymentService")
                        .build());

        if (transactionRefunded == null) {
            throw new BusinessException("Refund was not added. Please review your request.");
        }

        if (transactionRefunded.getTransactionStatus().equals(ETransactionStatus.FAILED)) {
            switch (transactionRefunded.getProvider()) {
                case PAYTABS:
                    clientRefundToDo.getContractPaymentConfirmationToDo().setPayTabsResponseMessage(transactionRefunded.getHasError());
                    break;
                case CHECKOUT:
                    clientRefundToDo.getContractPaymentConfirmationToDo().setCheckoutResponseMessage(transactionRefunded.getHasError());
                    break;

            }
            contractPaymentConfirmationToDoRepository.save(clientRefundToDo.getContractPaymentConfirmationToDo());
            logger.info("The refund failed, id: " + receivedTransaction.getId());
            sendEmail(clientRefundToDo, transactionRefunded.getProvider());
        }
    }

    public List<BackgroundTask> searchBGTApplyRefundOnlineCreditCardPaymentOnProvider(Long refundToDoId) {

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("name", "=", "applyRefundOnlineCreditCardPaymentOnProvider_" + refundToDoId);
        query.sortBy("creationDate",false);
        query.setLimit(1);

        return query.execute();
    }

    public void sendEmail(ClientRefundToDo clientRefundToDo, EPaymentProvider provider) {

        Map<String, String> parameters = new HashMap<>();
        parameters.put("client_id", clientRefundToDo.getContract().getClient().getId().toString());
        parameters.put("client_name", clientRefundToDo.getContract().getClient().getName());
        parameters.put("contract_id", clientRefundToDo.getContract().getId().toString());
        parameters.put("refund_amount", String.valueOf(clientRefundToDo.getAmount().intValue()));
        parameters.put("refund_purpose", clientRefundToDo.getPurpose().getName());
        parameters.put("provider", provider.toString());

        Setup.getApplicationContext().getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "credit_card_refund_failed_email",
                        parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CREDIT_CARD_REFUND_FAILED_EMAIL_RECIPIENTS),
                        "Refund Unauthorised from " + provider);
    }

    public User getDefaultCreditCardRefundManager() {
       return Setup.getRepository(UserRepository.class).findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_CREDIT_CARD_DEFAULT_MANAGER)));
    }

    public Map<String, Object> getReceivedTransactionAndTotalAllowedRefund(ClientRefundToDo clientRefundToDo, String transferNumber) {
        Map<String, Object> result = new HashMap<>();

        SelectQuery<EPaymentTransaction> q = new SelectQuery<>(EPaymentTransaction.class);
        q.filterBy("transReference", "=", transferNumber);
        q.filterBy("transactionStatus", "=", ETransactionStatus.SUCCESS);
        q.sortBy("creationDate", false, true);

        List<EPaymentTransaction> transactions = q.execute();

        if (transactions.isEmpty()) throw new BusinessException("Refund was not added. Please review your request.");

        EPaymentTransaction receivedTransaction = transactions.stream().filter(tr ->
            tr.getRelatedEntityId() != null && tr.getRelatedEntityType() != null &&
            tr.getRelatedEntityType().equals("ContractPaymentConfirmationToDo") &&
            contractPaymentConfirmationToDoRepository.existsById(tr.getRelatedEntityId()))
                .findFirst().orElse(transactions.get(0));

        logger.info("receivedTransaction id: " + receivedTransaction.getId());

        Double totalAmountRefunded = clientRefundTodoRepository.getTotalAmountRefundedByTransferReference(
                clientRefundToDo.getId(), clientRefundToDo.getContract(), receivedTransaction.getTransReference());
        Double allowRefundAmount = Math.max((receivedTransaction.getAmount() - (totalAmountRefunded == null ? 0.0 : totalAmountRefunded)), 0.0D);
        result.put("totalAmountRefunded", (totalAmountRefunded == null ? 0.0 : totalAmountRefunded));
        result.put("receivedTransaction", receivedTransaction);
        result.put("allowRefundAmount", allowRefundAmount);
        return result;
    }

    public boolean approveCreditCardRefund(ClientRefundToDo entity) {
        try {
            if (entity != null && entity.getNumberOfMonthlyPayments() != null
                    && entity.getNumberOfMonthlyPayments() > 0) {
                return true;
            }

            ContractPaymentConfirmationToDo toDo = Setup.getApplicationContext()
                    .getBean(ContractPaymentConfirmationToDoService.class)
                    .createConfirmationToDoForRefund(entity);
            if (toDo == null) return false;
            entity.setContractPaymentConfirmationToDo(toDo);

            refundOnlineCreditCardPayment(entity);
            return true;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    public User getApproverBySetup(ClientRefundSetup setup, ClientRefundPaymentMethod method) {
        User approver = setup != null && setup.getAutoApproved() &&
                ClientRefundPaymentMethod.CREDIT_CARD.equals(method) ?
                getDefaultCreditCardRefundManager() :
                setup != null ? setup.getApprovedBy() : null;

        logger.info("Approver: " +
                (approver != null ?
                        "Name: " + approver.getName() + ", ID: " + approver.getId() :
                        "NULL"));
        return approver;
    }

    // ACC-4326 -> ACC-6546 -> ACC-7120
    public void firstSecondaryPayrollPaidAcc7120(List<Map<String, Object>> l) {
        if (l == null || l.isEmpty()) return;

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("howMuchWeRefundOption", PicklistHelper.getItem("Absconder_Maid_Refund_Options", "refund_only_maid's_pending_salary"));
        }};

        l.forEach(map -> {
            try {
                Long housemaidPayrollLogId = Utils.parseValue(map.get("housemaidPayrollLogId"), Long.class);
                Long housemaidId = Utils.parseValue(map.get("housemaidId"), Long.class);
                List<Integer> paymentIds = (List<Integer>) map.get("paymentIds");

                Payment p = validateAddRefundAfterFirstSecondaryPayrollPaid(housemaidId, housemaidPayrollLogId,
                        paymentIds.stream().map(Integer::longValue).collect(Collectors.toList()));
                if (p == null) return;

                m.put("pendingSalaryRecord", java.sql.Date.valueOf(
                        new LocalDate(p.getDateOfPayment()).minusMonths(1).withDayOfMonth(1).toString("yyyy-MM-dd")));
                m.put("housemaidPayrollLogId", housemaidPayrollLogId);

                ClientRefundToDo clientRefundToDo = Setup.getApplicationContext()
                        .getBean(ClientMessagingAndRefundService.class)
                        .addClientRefund(p.getContract() , p.getContract().getClient(),
                                p.getWorkerSalary(), ClientRefundRequestType.ERP,
                                AccountingModule.PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP,
                                p, "Payment Received Event flow", m);

                // Time: On the next 2 PM after setting the refund request is added.
                long delay = new DateTime().isAfter(new DateTime().withTimeAtStartOfDay().withHourOfDay(14).withMinuteOfHour(30)) ?
                        (new DateTime().plusDays(1).withTimeAtStartOfDay().withHourOfDay(14).toDate().getTime() - new Date().getTime()) :
                        new DateTime().isAfter(new DateTime().withTimeAtStartOfDay().withHourOfDay(14)) ?
                                0 :
                                (new DateTime().withTimeAtStartOfDay().withHourOfDay(14).getMillisOfDay() - new DateTime().getMillisOfDay());

                Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                        .create(new BackgroundTask.builder(
                                "checkEVisaNotIssuedOrIssuedInThePreviousMonth" + new java.util.Date().getTime(),
                                "accounting",
                                "clientRefundService",
                                "checkEVisaNotIssuedOrIssuedInThePreviousMonth")
                                .withRelatedEntity("clientRefundToDo", clientRefundToDo.getId())
                                .withParameters(
                                        new Class[] {Long.class},
                                        new Object[] {clientRefundToDo.getId()})
                                .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                                .withDelay(delay)
                                .build());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private Payment validateAddRefundAfterFirstSecondaryPayrollPaid(
            Long housemaidId, Long housemaidPayrollLogId, List<Long> paymentIds) {

        if (paymentIds == null || paymentIds.isEmpty()) {
            logger.info("housemaidId: " + housemaidId + " no payments found");
            return null;
        }
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        logger.info("housemaidId: " + housemaidId +
                "; housemaidPayrollLogId: " + housemaidPayrollLogId +
                "; paymentIds: " + paymentIds.stream().map(Object::toString).collect(Collectors.joining(",")));

        Housemaid h = housemaidRepository.findOne(housemaidId);
        if (h == null) {
            logger.info("Housemaid not found -> exiting");
            return null;
        }

        Payment p = null;
        PaymentRequestPurpose purpose = paymentRequestPurposeRepository.findByName(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP));
        for (Long pId : paymentIds) {
            p = paymentRepository.findOne(pId);

            if (p != null && ContractService.isPreCollectedSalary(p.getContract())) {
                logger.info("contract id: " + p.getContract().getId() + " is pre-collected -> skip");
                return null;
            }

            if (p != null && clientRefundTodoRepository.existsForAcc7120(p.getContract().getId(), p.getId(),
                    housemaidPayrollLogId,
                    java.sql.Date.valueOf(new LocalDate(p.getDateOfPayment()).minusMonths(1).dayOfMonth()
                            .withMinimumValue().toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate(p.getDateOfPayment()).minusMonths(1).dayOfMonth()
                            .withMaximumValue().toString("yyyy-MM-dd")),
                    purpose.getId(),
                    Arrays.asList(ClientRefundStatus.PAID, ClientRefundStatus.PENDING, ClientRefundStatus.STOPPED))) {

                logger.info("There is non-rejected refund, payment id: " + p.getId() + "-> exiting");
                return null;
            }
        }

        return p;
    }

    // ACC-6546
    public void checkEVisaNotIssuedOrIssuedInThePreviousMonth(Long todoId) {
        logger.info("todoId id: " + todoId);

        // ACC-6833
        if (clientRefundTodoRepository.existsByIdAndStatusIn(todoId,
                Collections.singletonList(ClientRefundStatus.REJECTED))) return;

        ClientRefundToDo toDo = clientRefundTodoRepository.findOne(todoId);

        Map<String, String> parameters = new HashMap<>();
        parameters.put("greetings", toDo.getContract().isMaidCc() ?
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
        parameters.put("previous_month",
                new LocalDate(new LocalDate(toDo.getPendingSalaryRecord().getTime())).toString("MMMM"));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendMessageToClient(
                        toDo.getContract(),
                        parameters,
                        new ArrayMap<>(),
                        toDo.getId(),
                        toDo.getEntityType(),
                TemplateUtil.getTemplate(
                        MvNotificationTemplateCode.MV_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION.toString()));

        if (toDo.getContract().getHousemaid() == null) return;

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendMessageToMaid(
                        toDo.getContract(),
                        toDo.getContract().getHousemaid(),
                        TemplateUtil.getTemplate(
                                MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION.toString()),
                        new HashMap<>(),
                        toDo.getContract().getHousemaid().getId(),
                        toDo.getContract().getHousemaid().getEntityType());
    }

    // ACC-7120 ACC-4326
    public void markPayrollAsPaidAndRefunded(ClientRefundToDo clientRefundToDo) {
        if (clientRefundToDo.getHousemaidPayrollLogId() == null) return;

        logger.info("housemaidPayrollLogId: " + clientRefundToDo.getHousemaidPayrollLogId());
        Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .get("/payroll/HousemaidPayroll/markPayrollAsPaidAndRefunded/" +
                        clientRefundToDo.getHousemaidPayrollLogId(), String.class);

    }

    public void changedToPaid(ClientRefundToDo c) {

        logger.info("ClientRefundToDo id: " + c.getId());
        if (c.getContract() == null ||
                c.getContract().getId() == null ||
                c.getPurpose() == null ||
                (c.getPurpose().getName() == null && c.getPurpose().getId() == null) ||
                (c.getNumberOfMonthlyPayments() != null && c.getNumberOfMonthlyPayments() != 0) ||
                paymentRepository.existsByClientRefundToDo(c)) return;

        PaymentRequestPurposeRepository paymentRequestPurposeRepository = Setup.getRepository(PaymentRequestPurposeRepository.class);
        PaymentRequestPurpose purpose = c.getPurpose().getId() != null ?
                paymentRequestPurposeRepository.findOne(c.getPurpose().getId()) :
                paymentRequestPurposeRepository.findByName(c.getPurpose().getName());

        if (purpose.getTypeOfPayment() == null)  return ;

        logger.info("Start create payment");
        Payment payment = new Payment();
        payment.setAmountOfPayment(c.getAmount());
        payment.setMethodOfPayment(ClientRefundPaymentMethod.CREDIT_CARD.equals(c.getMethodOfPayment()) ?
                PaymentMethod.CARD : PaymentMethod.WIRE_TRANSFER);
        payment.setTypeOfPayment(purpose.getTypeOfPayment());
        payment.setDateOfPayment(new java.sql.Date(System.currentTimeMillis()));
        payment.setStatus(PaymentStatus.PDC);
        payment.setContract(c.getContract());
        payment.setClientRefundToDo(c);

        paymentService.forceCreatePayment(payment);
    }

    public boolean validateCreateTodoFromClientRefund(ClientRefundToDo clientRefundToDo, ClientRefundTodoType clientRefundTodoType) {
        boolean validTaskName = clientRefundToDo.getTaskName().contains(clientRefundTodoType.toString());
        boolean validNumberOfMonthlyPayments = clientRefundToDo.getNumberOfMonthlyPayments() == null || clientRefundToDo.getNumberOfMonthlyPayments() == 0;
        boolean validRequiredPayments = !clientRefundToDo.isConditionalRefund() ||
                clientRefundToDo.getRequiredPayments() == null ||
                !clientRefundToDo.getRequiredPayments().stream()
                        .map(p -> paymentRepository.findOne(p.getId()))
                        .anyMatch(p -> !p.getStatus().equals(PaymentStatus.RECEIVED) && !p.isReplaced());

        logger.info("validTaskName: " + validTaskName +
                "; validNumberOfMonthlyPayments: " + validNumberOfMonthlyPayments +
                "; validRequiredPayments: " + validRequiredPayments +
                "; checkRequiredPayments: " + clientRefundToDo.isCheckForRequiredPayments());

        return validTaskName && validNumberOfMonthlyPayments && (!clientRefundToDo.isCheckForRequiredPayments() || validRequiredPayments);
    }

    public void closeClientRefundTodo(ClientRefundToDo toDo, String status) {
        if (toDo == null) return;

        toDo.setStopped(true);
        toDo.setCompleted(true);
        toDo.setTaskName("");

        if (status.equals("APPROVED")) {
            toDo.setStatus(ClientRefundStatus.PAID);
            toDo.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));

        } else if (status.equals("REJECTED")) {
            toDo.setStatus(ClientRefundStatus.REJECTED);
            toDo.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));
        }

        clientRefundTodoRepository.save(toDo);

        //parentClientRefundTodo
        if (toDo.getParent() != null) {
            ClientRefundToDo parentClientRefundToDo = toDo.getParent();
            Integer numberOfChildTodos = clientRefundTodoRepository.findByParent(parentClientRefundToDo).size();
            Integer numberOfPendingChildTodos = clientRefundTodoRepository.findByParentAndStatus(parentClientRefundToDo, ClientRefundStatus.PENDING).size();
            int numberOfPaidChildTodos = clientRefundTodoRepository.findByParentAndStatus(parentClientRefundToDo, ClientRefundStatus.PAID).size();

            if (numberOfChildTodos.equals(parentClientRefundToDo.getNumberOfMonthlyPayments())
                    && numberOfPendingChildTodos.equals(0)) {

                parentClientRefundToDo.setStopped(true);
                parentClientRefundToDo.setCompleted(true);
                parentClientRefundToDo.setTaskName("");

                if (numberOfPaidChildTodos > 0) {
                    parentClientRefundToDo.setStatus(ClientRefundStatus.PAID);
                    parentClientRefundToDo.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));
                } else {
                    parentClientRefundToDo.setStatus(ClientRefundStatus.REJECTED);
                    parentClientRefundToDo.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));
                }

                clientRefundTodoRepository.save(parentClientRefundToDo);
            }
        }
    }

    public void createTodoUponAutoApprove(ClientRefundToDo clientRefundToDo) {

        try {
            logger.info("the Refund is Auto Apprrove");

            if (clientRefundToDo.getTaskName().equals(
                    ClientRefundTodoType.BANK_TRANSFER_CREATED.toString())) {

                PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
                payrollAccountantTodo.setClientRefundToDo(clientRefundToDo);
                Setup.getApplicationContext().getBean(PayrollAccountantTodoController.class)
                        .createEntity(payrollAccountantTodo);
            } else if (clientRefundToDo.getTaskName()
                    .equals(ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED.toString()) &&
                    validateCreateTodoFromClientRefund(clientRefundToDo, ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED)) {

                approveCreditCardRefund(clientRefundToDo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void validateConditionalRefund(ClientRefundToDo clientRefundToDo) {
        List<ClientRefundToDo> clientRefundToDos = clientRefundTodoRepository
                .findPendingRefundWithRequiredPaymentsByContract(clientRefundToDo.getContract());

        Double totalAmount = clientRefundToDo.getAmount();
        Set<Long> s = new HashSet<>();

        Double totalRelatedPaymentAmount = Setup.getRepository(PaymentRepository.class)
                .sumAmountOfPaymentByIds(clientRefundToDo.getRequiredPayments().stream()
                        .map(p -> {
                            s.add(p.getId());
                            return p.getId();
                        })
                        .collect(Collectors.toList()));

        for (ClientRefundToDo todo : clientRefundToDos) {
            totalRelatedPaymentAmount += todo.getRequiredPayments()
                    .stream()
                    .filter(p -> {
                        if (s.contains(p.getId())) return false;
                        s.add(p.getId());
                        return true;
                    })
                    .mapToDouble(Payment::getAmountOfPayment).sum();
            totalAmount += todo.getAmount();
        }

        if (totalAmount > totalRelatedPaymentAmount) {
            throw new BusinessException("The sum of conditional refunds is more than the sum of Payments, " +
                    "please review all conditional refunds related to the contract");
        }
    }

    // ACC-8308
    public ClientRefundToDo validateAndPrepareClientRefundByGPT(Map<String, Object> body) {

        ClientRefundToDo clientRefundToDo = new ClientRefundToDo();
        // 1-Validate Notes
        if (!body.containsKey("notes") || body.get("notes") == null ||
                body.get("notes").toString().isEmpty()) throw new BusinessException("Notes should not be empty!");

        // 2-Validate Payment Method
        if (!body.containsKey("paymentMethod") || body.get("paymentMethod") == null)
            throw new BusinessException("Payment method is not either “Bank Transfer” or “Credit Card”");

        ClientRefundPaymentMethod paymentMethod;
        try {
            paymentMethod = ClientRefundPaymentMethod.valueOf((String) body.get("paymentMethod"));
        } catch (Exception e) {
            throw new BusinessException("Payment method is not either “Bank Transfer” or “Credit Card”");
        }

        // 3-Validate Amount
        Double amount = !body.containsKey("amount") || body.get("amount") == null ? null :
                Utils.parseValue(body.get("amount"), Double.class);
        if (amount == null || amount <= 0D) {
            throw new BusinessException("The amount is not a positive double");
        }

        // 4-Validate Contract
        if (!body.containsKey("contractId") || body.get("contractId") == null)
            throw new BusinessException("Contract should not be empty!");

        logger.info("contract id:" + body.get("contractId"));
        Contract contract = contractRepository.findOne(Utils.parseValue(body.get("contractId"), Long.class));

        if (contract == null) throw new BusinessException("Contract not found!");

        // 4-Validate Bank or Card Info optional
        if (paymentMethod.equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
            if (!body.containsKey("checkoutPaymentID") || body.get("checkoutPaymentID") == null) {
                throw new BusinessException("CheckoutPaymentID is missing from the api request or is invalid");
            }

            clientRefundToDo.setTransferReference((String) body.get("checkoutPaymentID"));
        } else if (paymentMethod.equals(ClientRefundPaymentMethod.BANK_TRANSFER)) {
            // get Bank info from API
            if (body.containsKey("accountName") &&
                    body.get("accountName") != null) { clientRefundToDo.setAccountName(String.valueOf(body.get("accountName"))); }

            if (body.containsKey("iban") &&
                    body.get("iban") != null) { clientRefundToDo.setIban(String.valueOf(body.get("iban"))); }

            // get Bank info from Client Info
            if (!body.containsKey("accountName") || body.get("accountName") == null ||
                     !body.containsKey("iban") || body.get("iban") == null) {
                List<Object[]> o = Setup.getRepository(DirectDebitRepository.class)
                                        .getClientAccountInfo(contract.getClient().getId());
                if (o.isEmpty()) throw new BusinessException("Iban and AccountName are missing from the api request or are invalid");

                logger.info("AccountName id: " + o.get(0)[1] + " ; Iban: " + o.get(0)[2]);
                if (!body.containsKey("accountName") || body.get("accountName") == null) { clientRefundToDo.setAccountName(String.valueOf(o.get(0)[1])); }
                if (!body.containsKey("iban") || body.get("iban") == null) { clientRefundToDo.setIban(String.valueOf(o.get(0)[2])); }
            }

            // Check if Iban number is valid
            try {
                Map<String, Object> r = ContractPaymentTermHelper.checkIBAN(clientRefundToDo.getIban());
                Map<String, Object> validationIban = (Map<String, Object>) ((Map<String, Object>) r.get("validations")).get("iban");
                if (!r.containsKey("validations") ||
                        !String.valueOf(validationIban.get("code")).equals("001")) {
                    throw new BusinessException(String.valueOf(validationIban.get("message")));
                }
            } catch (IOException e) {
                throw new BusinessException("Invalid IBAN number");
            }
        }

        // 5-Validate Purpose
        if (!body.containsKey("purposeName") || body.get("purposeName") == null)
            throw new BusinessException("The purpose is not a valid predefined purpose");

        logger.info("Purpose Name:" + body.get("purposeName"));

        List<String> purposes = Arrays.stream(
                String.valueOf(
                        Setup.getParameter(Setup.getCurrentModule(), contract.isMaidCc() ?
                                AccountingModule.PARAMETER_NGPT_REFUND_PURPOSES_FOR_CC_CONTRACT :
                                AccountingModule.PARAMETER_NGPT_REFUND_PURPOSES_FOR_MV_CONTRACT))
                        .split(";"))
                .map(String::trim)
                .collect(Collectors.toList());

        if (!purposes.contains(String.valueOf(body.get("purposeName")))) throw new BusinessException("The purpose is invalid");

        PaymentRequestPurpose purpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findByForClientAndNameEquals(true, String.valueOf(body.get("purposeName")))
                .stream().findFirst().orElse(null);

        if (purpose == null) throw new BusinessException("The purpose is not a valid predefined purpose");

        ClientRefundSetup setup = purpose.getUniquePurposeSetup(null);

        // 6-Validate AttachProof optional
        if (setup.getRequireAttachment() && !body.containsKey("attachProof")) {
            throw new BusinessException("Attach proof is missing from the api request or is invalid");
        }

        if (body.containsKey("attachProof")) {
           String attId = !body.get("attachProof").toString().contains("#file:") ?
                    body.get("attachProof").toString() :
                    body.get("attachProof").toString().split("#file:")[1];

           Attachment att = Setup.getRepository(AttachementRepository.class).findOne(Utils.parseValue(attId, Long.class));
           if (att == null) {
               throw new BusinessException("Attach proof is missing from the api request or is invalid");
           }

           if (att.getTag() == null || !ClientRefundToDo.PROOF_ATTACHMENT_TAG.equals(att.getTag())) {
               att.setTag(ClientRefundToDo.PROOF_ATTACHMENT_TAG);
               att = Setup.getRepository(AttachementRepository.class).save(att);
            }

           clientRefundToDo.addAttachment(att);
        }

        clientRefundToDo.setNotes((String) body.get("notes"));
        clientRefundToDo.setAmount(amount);
        clientRefundToDo.setMethodOfPayment(paymentMethod);
        clientRefundToDo.setContract(contract);
        clientRefundToDo.setClient(contract.getClient());
        clientRefundToDo.setPurpose(purpose);
        clientRefundToDo.setNumberOfUnusedDays(Utils.parseValue(
                body.getOrDefault("numberOfUnusedDays", null), Integer.class));

        // ACC-9042
        // 7- Validate conditionalRefund optional
        if (body.containsKey("conditionalRefund") && Boolean.parseBoolean((String) body.get("conditionalRefund"))) {
            List<Long> ids;

            if (!body.containsKey("linkedPayment")) {
                if (!body.containsKey("paymentDate") || body.get("paymentDate") == null) {
                    throw new BusinessException("Payment date is required in case of a conditional refund when payment IDs are not provided.");
                }

                Date paymentDate = new LocalDate(body.get("paymentDate").toString()).toDate();
                List<Map<String, Object>> l = paymentRepository.findMonthlyPaymentsByContractAndDate(contract, paymentDate);

                if (l.isEmpty()) {
                    throw new BusinessException("No monthly payments found for the specified date: " + paymentDate);
                }

                ids = l.stream()
                        .map(x -> Utils.parseValue(x.get("id"), Long.class))
                        .collect(Collectors.toList());

                if (paymentMethod.equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                    // First check for CREDIT_CARD payments, if found more than one take their IDs, else take the first of any
                    if (l.stream().anyMatch(x -> PaymentMethod.CARD.name().equals(x.get("methodOfPayment").toString()))) {
                        ids = l.stream()
                                .filter(x -> PaymentMethod.CARD.name().equals(x.get("methodOfPayment").toString()))
                                .map(x -> Utils.parseValue(x.get("id"), Long.class))
                                .collect(Collectors.toList());
                    }
                } else {
                    if (l.stream().anyMatch(x -> PaymentMethod.DIRECT_DEBIT.name().equals(x.get("methodOfPayment").toString()))) {
                        ids = l.stream()
                                .filter(x -> PaymentMethod.DIRECT_DEBIT.name().equals(x.get("methodOfPayment").toString()))
                                .map(x -> Utils.parseValue(x.get("id"), Long.class))
                                .collect(Collectors.toList());
                    }
                }
            } else {
                ids = Arrays.stream(((String) body.get("linkedPayment")).split(","))
                        .filter(id -> !id.isEmpty()).map(Long::parseLong).collect(Collectors.toList());
            }

            if (ids.isEmpty()) throw new BusinessException("you must link refund with 1 PDP payment at least.");

            logger.info("ids found " + ids.stream().map(String::valueOf).collect(Collectors.joining(",")));

            if (!QueryService.existsEntity(Payment.class, "e.id in :p0 and e.contract.id = :p1",
                    new Object[]{ ids, contract.getId() })) {
                throw new BusinessException("One of the linked payments doesn't relate to the passed contract");
            }

            clientRefundToDo.setConditionalRefund(true);
            List<Payment> payments = paymentRepository.findAll(ids);

            if (payments.stream().anyMatch(p ->
                    !Arrays.asList(PaymentStatus.PRE_PDP, PaymentStatus.PDC).contains(p.getStatus()))) {
                throw new BusinessException("Only payments with a status of PDP or PRE_PDP are permitted.");
            }

            if (!payments.isEmpty()) {
                clientRefundToDo.setRequiredPayments(payments);
                validateConditionalRefund(clientRefundToDo);
            }
        }

        return clientRefundToDo;
    }

    // ACC-8479
    public boolean checkFinishedBGTRefundOrRetryRefundedBusinessLogic(EPaymentTransaction transactionRefunded, ClientRefundToDo clientRefundToDo) {

        if (QueryService.existsEntity(BackgroundTask.class,
                "e.name = :p0 and e.status in :p1",
                new Object[]{"applyRefundOnlineCreditCardPaymentOnProvider_" + clientRefundToDo.getId(),
                        Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)})) {
            return true;
        }

        // Retry call ApplyOnRefundedBusinessLogic after finished approval refund process
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "retryApplyOnRefundedBusinessLogic_" + transactionRefunded.getId(),
                        "accounting",
                        "accountingEPaymentService", "applyOnRefundedBusinessLogic")
                        .withParameters(new Class[] { Long.class }, new Object[] { transactionRefunded.getId() })
                        .withRelatedEntity("EPaymentTransaction", transactionRefunded.getId())
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(3 * 60 * 1000L)
                        .build());
        return false;
    }

    public void sendEmailForRejectedRefundRequests(ClientRefundToDo clientRefundToDo) {

        String mainEmails = clientRefundToDo.getRequesterUser().getEmail() != null ?
                clientRefundToDo.getRequesterUser().getEmail() : "";

        String cc = Setup.getParameter(Setup.getCurrentModule(),
                clientRefundToDo.getContract().isMaidCc() ?
                        AccountingModule.PARAMETER_EXTRA_EMAILS_FOR_REJECTED_REFUND_CC_CONTRACT :
                        AccountingModule.PARAMETER_EXTRA_EMAILS_FOR_REJECTED_REFUND_MV_CONTRACT);

        if (mainEmails.isEmpty() && cc.isEmpty()) return;

        if (mainEmails.isEmpty()) {
            mainEmails = cc;
            cc = "";
        }

        Map<String, String> parameters = new HashMap<>();
        parameters.put("client_id", clientRefundToDo.getContract().getClient().getId().toString());
        parameters.put("client_name", clientRefundToDo.getContract().getClient().getName());
        parameters.put("contract_id", clientRefundToDo.getContract().getId().toString());
        parameters.put("refund_id", clientRefundToDo.getId().toString());
        parameters.put("user_name", CurrentRequest.getUser() != null ?
                CurrentRequest.getUser().getName() : "");
        parameters.put("user_email", CurrentRequest.getUser() != null && CurrentRequest.getUser().getEmail() != null ?
                CurrentRequest.getUser().getEmail() : "");
        parameters.put("rejected_at_time", new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
        parameters.put("reason_of_rejection", clientRefundToDo.getManagerNotes() != null ? clientRefundToDo.getManagerNotes() : "");

        Setup.getApplicationContext().getBean(MessagingService.class)
                .sendEmailToOfficeStaffWithCc(
                        null,
                        "rejected_refund_requests_email",
                        parameters,
                        mainEmails,
                        EmailHelper.removeDuplicatedEmails(mainEmails, cc),
                        "Refund Request " + clientRefundToDo.getId().toString() + " for Client " +
                                clientRefundToDo.getContract().getClient().getName() + " was rejected");
    }

    public void sendNotificationToManagerUponRefundCreated(ClientRefundToDo clientRefundToDo, PaymentRequestPurpose requestPurpose) {

        if (clientRefundToDo.getTaskName() == null ||
                !clientRefundToDo.getTaskName().equals(ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString()) ||
                requestPurpose == null) return;

        ClientRefundSetup setup = requestPurpose.getUniquePurposeSetup();
        if (setup == null || setup.getApprovedBy() == null) return;

        NotificationService notificationService = Setup.getApplicationContext().getBean(NotificationService.class);
        ClientRepository clientRepository = Setup.getRepository(ClientRepository.class);

        logger.info("enter sendERPNotificationToApprover");

        Client client = clientRepository.findOne(clientRefundToDo.getClient().getId());

        String notificationBody = "A new client refund was added by " + clientRefundToDo.getRequesterUserName() +
                " for " + client.getName() + ", and it is waiting for your approval.";

        String link = "/accounting/client-refund-approvals/details/" + clientRefundToDo.getId();

        // send notification
        Notification notification = new Notification();
        notification.setTitle("new refund request is added");
        notification.setBody(notificationBody);
        notification.setLink(link);
        notification.setDate(new Date());
        notification.setModule(Setup.getCurrentModule());
        notification.setUser(requestPurpose.getUniquePurposeSetup().getApprovedBy());

        notificationService.sendNotification(notification);
    }

    public String getPaymentStatus(Map<String, Object> payment) {
        if (payment.get("status") == null) return null;

        if (payment.get("status").equals(PaymentStatus.BOUNCED) &&
                payment.get("replaced") != null && Boolean.parseBoolean(payment.get("replaced").toString())) {
            return "Bounced - Replaced";
        }
        return ((PaymentStatus) payment.get("status")).getLabel();
    }
}