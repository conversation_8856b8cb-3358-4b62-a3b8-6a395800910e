package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CashierRepository extends BaseRepository<User> {

    /**
     * Find users with active cash boxes
     */
    @Query("select u from User u " +
            "inner join Bucket b on b.holder.id = u.id " +
            "where u.activated = true and b.bucketType = 'CASH_BOX' and b.isActive = true")
    Page<User> findUsersWithActiveCashBoxes(Pageable pageable);
}
