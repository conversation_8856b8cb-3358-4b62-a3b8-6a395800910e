package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.TransactionDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <PERSON> at Nov 24, 2017
 */
@Repository
public interface ExpenseRepository extends BaseRepository<Expense> {

    Boolean existsByCode(String code);

    Boolean existsByName(String name);

    Expense findByCodeAndDeletedFalse(String code);

    @Query("select count(b)>0 from Expense b WHERE b.code =?2 AND b.id != ?1")
    Boolean existsBeforeByCode(Long id, String code);

    @Query("select count(b)>0 from Expense b WHERE b.name =?2 AND b.id != ?1")
    Boolean existsBeforeByName(Long id, String name);

    @Query("SELECT SUM (amount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.date >= ?2 AND t.date <= ?3")
    Double calculateExpensesBetweenTwoDates(
            Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (amount - (CASE WHEN vatAmount IS NOT NULL THEN vatAmount ELSE 0 END)) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 AND (t.accrual = null OR t.accrual = false)")
    Double calculateExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (amount - (CASE WHEN vatAmount IS NOT NULL THEN vatAmount ELSE 0 END)) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.date >= ?2 AND t.date <= ?3 AND (t.accrual = null OR t.accrual = false)")
    Double calculateExpensesBetweenTwoTransactionDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 AND vatAmount IS NOT NULL")
    Double calculateExpensesVatAmountBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.date >= ?2 AND t.date <= ?3 AND vatAmount IS NOT NULL")
    Double calculateExpensesVatAmountBetweenTwoTransactionDates(Expense expense, Date transactionDate1, Date transactionDate2);

    // ACC-1389
    @Query("SELECT SUM (td.transactionAmount - (CASE WHEN t.vatAmount IS NOT NULL THEN t.vatAmount ELSE 0 END)) FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE td.transactionAmount != 0 AND t.expense = ?1 AND t.expense.deleted = false AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    Double calculateAccrualExpensesBetweenTwoDates(
            Expense expense, Date transactionDate1, Date transactionDate2);

    // ACC-1389
    @Query("SELECT SUM (td.averageAmount) FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE t.expense = ?1 AND t.expense.deleted = false AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    Double calculateExpensesAverageBetweenTwoDates(Expense expense, Date transactionDate1, Date transactionDate2);

    // ACC-1389
    @Query("SELECT SUM (td.profitAdjustment) FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE t.expense = ?1 AND t.expense.deleted = false AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    Double calculateExpensesProfitAdjustmentBetweenTwoDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.IN")
    Double calculateInputVATExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.date >= ?2 AND t.date <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.IN")
    Double calculateInputVATExpensesBetweenTwoTransactionDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.OUT")
    Double calculateOutputVATExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.expense = ?1 AND t.expense.deleted = false AND t.date >= ?2 AND t.date <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.OUT")
    Double calculateOutputVATExpensesBetweenTwoTransactionDates(Expense expense, Date transactionDate1, Date transactionDate2);

    // ACC-496 2) Get all Expenses-transactions behind a row in P&Ls page | Majd Bousaad
    @Query("SELECT t FROM Transaction t "
            + "WHERE (t.accrual = null OR t.accrual = false) AND t.expense = ?1 AND t.expense.deleted = false AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3")
    List<Transaction> getExpenseTransactionsBetweenTwoPnlDates(Expense expense, Date fromDate, Date toDate);

    @Query("SELECT t FROM Transaction t "
            + "WHERE (t.accrual = null OR t.accrual = false) AND t.expense = ?1 AND t.expense.deleted = false AND t.date >= ?2 AND t.date <= ?3")
    List<Transaction> getExpenseTransactionsBetweenTwoTransactionDates(Expense expense, Date fromDate, Date toDate);

    // ACC-1389
    @Query("SELECT td FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE t.expense = ?1 AND t.expense.deleted = false AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    List<TransactionDetails> getExpenseTransactionDetailsBetweenTwoDates(Expense expense, Date fromDate, Date toDate);

    Expense findOneByCode(String code);

    @Query("select e from Expense e " +
            "left join e.requestors r " +
            "where e.requestedFrom = :requestedFrom and (r is null or r.id = :requestorId) and e.deleted = false and e.disabled = false and " +
                "(:search is null or e.code like concat('%', :search, '%') or " +
                "e.caption like concat('%', :search, '%') or e.name like concat('%', :search, '%'))")
    List<Expense> findByRequestedFromAndDeletedFalseAndDisabledFalseAndRequestorsInAndName(
            @Param("requestedFrom") PicklistItem requestedFrom,
            @Param("requestorId") Long requestorId, @Param("search") String search);

    List<Expense> findByParent(Expense expense);

    @Query("select e.code, e.id,  " +
            "CASE " +
            "when e.deleted = true THEN CONCAT(e.name, ' (Deleted)')  " +
            "when e.disabled = true then CONCAT(e.name, ' (Disabled)') " +
            "ELSE e.name END " +
            "from Expense e")
    List<Object[]> findIdAndNameByCode();

    @Query("select child.code " +
            "from Expense child " +
            "where child.parent.id = ?1")
    List<String> findAllCodeChildByParent(Long expenseId);

    @Query("select distinct case " +
            "when t is not null " +
                "then concat('Pending ', r.approveHolder.fullName) " +
            "when r.approveHolderEmail is not null " +
                "then concat('Pending ', r.approveHolderEmail) " +
            "else ' ' end " +
            "from ExpenseRequestTodo r " +
            "left join r.approveHolder t " +
            "where r.status = 'PENDING' and r.taskName = 'WAITING_MANAGER_APPROVAL' and " +
            "(r.approveHolder is not null or r.approveHolderEmail is not null)")
    List<String> findApproveHolderEmailsForPendingForApprovalFieldSearch();
}
