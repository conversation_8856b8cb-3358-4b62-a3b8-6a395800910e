package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.*;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowProgressPeriod;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig.RequiredAction;
import com.magnamedia.extra.Utils;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.extra.annotations.UsedBy.Modules;
import com.magnamedia.extra.annotations.UsedBy.Others;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Hachem
 */

@RestController
@RequestMapping("/flowProcessor")
public class FlowProcessorController extends BaseRepositoryController<FlowEventConfig> {
    private static final Logger logger = Logger.getLogger(FlowProcessorController.class.getName());

    @Autowired
    private FlowProgressPeriodRepository flowProgressPeriodRepository;
    @Autowired
    private FlowSubEventConfigRepository flowSubEventConfigRepository;
    @Autowired
    private FlowEventConfigRepository flowEventConfigRepository;
    @Autowired
    private DDMessagingRepository ddMessagingRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private TagRepository tagRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private TemplateUtil templateUtil;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;

    @Override
    public BaseRepositoryParent<FlowEventConfig> getRepository() {
        return Setup.getRepository(FlowEventConfigRepository.class);
    }

    @Override
    protected ResponseEntity<?> updateEntity(FlowEventConfig entity) {

        if (!entity.getTags().isEmpty()) {
            List<String> keys = Arrays.asList(
                    "waitBeforeTerminationInHours",
                    "stopSecondMessageContractStartDay",
                    "stopMaxTrialMinusOneOnMonth",
                    "day_of_send_dd_signing_offer",
                    "monthly_reminder_paying_cc_start_before_x_days",
                    "dayOfStartPaymentReminderOneMonthAgreement",
                    "dayOfStartSigningOfferOneMonthAgreement",
                    "defaultDDSendTime",
                    "ipam_x_days_before_paid_end_date",
                    "ipam_x_days_before_adjusted_end_date",
                    "mv_ipam_x_paid_payments_to_convert_paying_cc",
                    "cc_ipam_x_paid_payments_to_convert_paying_cc",
                    "extension_flow_start_after_x_days");

            List<Tag> updatedTags = tagRepository.findAll(entity.getTags().stream()
                .map(BaseEntity::getId).collect(Collectors.toList()));
            entity.setTags(updatedTags);

            keys.forEach(key -> {
                if (!entity.hasTag(key)) return;
                List<Tag> tags = entity.getTags().stream()
                    .filter(tag -> tag.getKey().equals(key) && tag.isComplex()).collect(Collectors.toList());
                if (tags.size() <= 1) return;

                tags.remove(tags.size() - 1);
                entity.getTags().removeAll(tags);
                tagRepository.deleteAll(tags);
            });
        }

        return super.updateEntity(entity);
    }

    @PreAuthorize("hasPermission('flowProcessor','subEventConfigs')")
    @GetMapping("/subEventConfigs/{id}")
    public ResponseEntity<?> getSubEventConfigs(
            @PathVariable(name = "id") FlowEventConfig flowEventConfig) {

        return new ResponseEntity<>(flowEventConfig.getSubEventConfigs().stream()
                .filter(s -> !s.getName().isDeprecated())
                .collect(Collectors.toList()), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('flowProcessor','progressPeriods')")
    @GetMapping("/progressPeriods/{id}")
    public ResponseEntity<?> getProgressPeriods(
            @PathVariable(name = "id") FlowSubEventConfig flowSubEventConfig) {
        
        return new ResponseEntity<>(flowSubEventConfig.getProgressPeriods(), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('flowProcessor','savePeriods')")
    @PostMapping("/savePeriods/{id}")
    public ResponseEntity<?> savePeriods(@PathVariable(name = "id") FlowSubEventConfig flowSubEventConfig,
                                         @RequestBody List<FlowProgressPeriod> progressPeriods,
                                         @RequestParam(name = "defaultPeriod", required = false, defaultValue = "0") Integer defaultPeriod) {

        flowSubEventConfig.setDefaultPeriod(defaultPeriod);
        flowSubEventConfigRepository.save(flowSubEventConfig);

        flowSubEventConfig.getProgressPeriods().forEach(oldPeriod -> {
            if (progressPeriods.stream().noneMatch(newPeriod -> Objects.equals(newPeriod.getId(), oldPeriod.getId())))
                flowProgressPeriodRepository.delete(oldPeriod);
        });

        progressPeriods.forEach(p -> flowProgressPeriodRepository.save(p));
        
        return okResponse();
    }

    @PreAuthorize("hasPermission('flowProcessor','afterCashFlowSetup')")
    @GetMapping("/afterCashFlowSetup")
    @Transactional
    public ResponseEntity<?> afterCashFlowSetup() {

        if (tagRepository.findByNameIgnoreCase("waitBeforeTerminationInHours").isEmpty()) {
            Tag tag1 = new Tag();
            tag1.setName("waitBeforeTerminationInHours");
            tagRepository.save(tag1);
        }

        if (tagRepository.findByNameIgnoreCase("stopSecondMessageContractStartDay").isEmpty()) {
            Tag tag2 = new Tag();
            tag2.setName("stopSecondMessageContractStartDay");
            tagRepository.save(tag2);
        }

        if (tagRepository.findByNameIgnoreCase("stopMaxTrialMinusOneOnMonth").isEmpty()) {
            Tag tag3 = new Tag();
            tag3.setName("stopMaxTrialMinusOneOnMonth");
            tagRepository.save(tag3);
        }

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
        if (flowEventConfig == null) {
            flowEventConfig = new FlowEventConfig();
            flowEventConfig.setName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
            flowEventConfig.setMaxFlowRuns(120);
            flowEventConfig.setCancellationReason(flowProcessorService.createOrFetchCancellationReason(
                    Contract.DUE_IPAM_FLOW_TERMINATION_REASON, "Due IPAM Flow"));
            flowEventConfig = flowEventConfigRepository.save(flowEventConfig);

        }

        FlowSubEventConfig noSignatureSubEvent = flowSubEventConfigRepository
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flowEventConfig);
        if (noSignatureSubEvent == null) {
            noSignatureSubEvent = new FlowSubEventConfig();
            noSignatureSubEvent.setName(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE);
            noSignatureSubEvent.setFlowEventConfig(flowEventConfig);
            noSignatureSubEvent.setRequiredAction(RequiredAction.CLIENT_PROVIDES_SIGNATURE);
            noSignatureSubEvent.setTerminateContractOnMaxTrials(true);
            noSignatureSubEvent.setTerminateContractOnMaxReminders(true);
            noSignatureSubEvent.setMaxTrials(9);
            noSignatureSubEvent = flowSubEventConfigRepository.save(noSignatureSubEvent);
        }
    
        List<FlowProgressPeriod> noSignatureSubEventPeriods =
            flowProgressPeriodRepository.findByFlowSubEventConfig(noSignatureSubEvent);
        flowProgressPeriodRepository.delete(noSignatureSubEventPeriods);
        
        for (int index = 1; index < 10 ; index++) {
            FlowProgressPeriod flowProgressPeriod = new FlowProgressPeriod();
            flowProgressPeriod.setFlowSubEventConfig(noSignatureSubEvent);
            flowProgressPeriod.setReminders(1);
            flowProgressPeriod.setTrials(index);
            switch (index) {
                case 1:
                    flowProgressPeriod.setPeriodInHours(240);
                    break;
                case 2:
                case 3:
                case 4:
                    flowProgressPeriod.setPeriodInHours(168);
                    break;
                case 5:
                case 6:
                    flowProgressPeriod.setPeriodInHours(48);
                    break;
                case 7:
                case 8:
                    flowProgressPeriod.setPeriodInHours(24);
                    break;
                case 9:
                    flowProgressPeriod.setPeriodInHours(0);
                    break;
            }
            flowProgressPeriodRepository.save(flowProgressPeriod);
        }
        
        //ACC-4909
        FlowSubEventConfig noSignatureOnePaymentOnly = flowSubEventConfigRepository
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY, flowEventConfig);
        if (noSignatureOnePaymentOnly == null) {
            noSignatureOnePaymentOnly = new FlowSubEventConfig();
            noSignatureOnePaymentOnly.setName(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY);
            noSignatureOnePaymentOnly.setFlowEventConfig(flowEventConfig);
            noSignatureOnePaymentOnly.setRequiredAction(RequiredAction.CLIENT_PROVIDES_SIGNATURE);
            noSignatureOnePaymentOnly.setTerminateContractOnMaxTrials(false);
            noSignatureOnePaymentOnly.setTerminateContractOnMaxReminders(false);
            noSignatureOnePaymentOnly.setMaxTrials(1);
            noSignatureOnePaymentOnly = flowSubEventConfigRepository.save(noSignatureOnePaymentOnly);
        }
        
        List<FlowProgressPeriod> noSignatureOnePaymentOnlyPeriods =
                flowProgressPeriodRepository.findByFlowSubEventConfig(noSignatureOnePaymentOnly);
        flowProgressPeriodRepository.delete(noSignatureOnePaymentOnlyPeriods);
        
        FlowProgressPeriod noSignatureOnePaymentOnlyPeriod = new FlowProgressPeriod();
        noSignatureOnePaymentOnlyPeriod.setFlowSubEventConfig(noSignatureOnePaymentOnly);
        noSignatureOnePaymentOnlyPeriod.setReminders(1);
        noSignatureOnePaymentOnlyPeriod.setTrials(1);
        noSignatureOnePaymentOnlyPeriod.setPeriodInHours(0);
        flowProgressPeriodRepository.save(noSignatureOnePaymentOnlyPeriod);
        
        return okResponse();
    }

    @PreAuthorize("hasPermission('flowProcessor','migrationAfterCashFlowDdMessaging')")
    @GetMapping("/migrationAfterCashFlowDdMessaging")
    public ResponseEntity<?> migrationAfterCashFlowDdMessaging() {
        SelectQuery<DDMessaging> ddMessagingSelectQuery = new SelectQuery<>(DDMessaging.class);
        ddMessagingSelectQuery.filterBy("event", "=", DDMessagingType.ClientPaidCashAndNoSignatureProvided);
        List<DDMessaging> ddMessagingList = ddMessagingSelectQuery.execute();
        
        if (ddMessagingList.isEmpty())
            return new ResponseEntity<>("There is no dd messaging", HttpStatus.BAD_REQUEST);
        
        updateNoSignatureDdMessaging(ddMessagingList);

        boolean newDDCreated = ddMessagingList.stream()
            .filter(ddMessaging -> ddMessaging.getTrials().equals("5")
                && ddMessaging.getSendPayTabMessage())
            .findFirst().orElse(null) != null;
        if (!newDDCreated) createNewDdMessageForAfterCashFlow();
        
        createNoSignatureOnePaymentOnlyMessage(ddMessagingList); // ACC-4909
        
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }
    
    public void updateNoSignatureDdMessaging(List<DDMessaging> ddMessagingList) {
        ddMessagingList.stream()
            .filter(ddMessaging -> ddMessaging.getSubType() == null ||
                !ddMessaging.getSubType().equals(
                    DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY))
            .forEach(ddMessaging -> {
                logger.log(Level.INFO, "old dd id: {0}", ddMessaging.getId());
                ddMessaging.setActive(false);
                ddMessaging.setCreateHumanSms(false);
                ddMessaging.setHumanSmsTitle(null);
                ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
                ddMessaging.setSendToMaid(false);

                switch (ddMessaging.getWhenToStartSending()) {
                    case FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION:
                        if (ddMessaging.getContractProspectTypes().equals("maidvisa.ae_prospect"))
                            updateNoSignatureDdMessagingMvFirstOfNextMonthOfContactCreation(ddMessaging);
                        else updateNoSignatureDdMessagingCcFirstOfNextMonthOfContactCreation(ddMessaging);
                        break;
                    case DIRECTLY_ON_CONTACT_CREATION:
                        if (ddMessaging.getContractProspectTypes().equals("maidvisa.ae_prospect"))
                            updateNoSignatureDdMessagingMvDirectlyOnContactCreation(ddMessaging);
                        else updateNoSignatureDdMessagingCcDirectlyOnContactCreation(ddMessaging);
                        break;
                }

                ddMessaging.setParameterToUsed("@greetings@,@dear_receiver_name@,@link_send_dd_details@");
                ddMessaging.setReminders("1");
                ddMessaging.setSubType(DDMessagingSubType.NO_SIGNATURE);
                ddMessaging.setSendTime(null);
                ddMessagingRepository.save(ddMessaging);
            });
    }

    public void updateNoSignatureDdMessagingCcDirectlyOnContactCreation(DDMessaging ddMessaging) {
        logger.log(Level.INFO, "old dd id: {0}", ddMessaging.getId());
        ddMessaging.setTrials("1");
        ddMessaging.setActive(true);

        Template notificationTemplate = ddMessaging.getClientTemplate();
        notificationTemplate.setText("Your payment covers your maid's service until @paid_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. Please whenever your " +
            "Emirates ID and IBAN are ready, please click \"Sign Now\" to complete your Monthly " +
            "Bank Payment Form.");
        templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

        Template smsTemplate = templateRepository.findByNameIgnoreCase(
                notificationTemplate.getNotificationSmsTemplateName());
        smsTemplate.setText("@greetings@,\n" +
            "Your payment covers your maid's service until @paid_end_date@. Please note that for future months, a " +
            "Monthly Bank Payment Form will be needed. Please whenever your Emirates ID and IBAN are " +
            "ready, please complete your Monthly Bank Payment Form by clicking on the following link: " +
            "@link_send_dd_details@");
        templateUtil.updateTemplate(smsTemplate, new HashMap<>());
    }

    public void updateNoSignatureDdMessagingCcFirstOfNextMonthOfContactCreation(DDMessaging ddMessaging) {
        logger.log(Level.INFO, "old dd id: {0}", ddMessaging.getId());

        if (ddMessaging.getAfterDays() == 0) {
            ddMessaging.setActive(true);
            ddMessaging.setTrials("2");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("We just want to remind you that for future months, a Monthly " +
                "Bank Payment Form will be needed. Please whenever your Emirates ID and IBAN are ready, " +
                "complete your Monthly Bank Payment Form by clicking on “Sign Now” below.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(
                    notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@,\n" +
                "We just want to remind you that for future months, a Monthly Bank Payment Form will be " +
                "needed. Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank " +
                "Payment Form by clicking on the following link: @link_send_dd_details@");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 2, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 5) {
            ddMessaging.setTrials("3");
            ddMessaging.setActive(true);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("We just want to remind you that for future months, a Monthly " +
                "Bank Payment Form will be needed. Whenever your Emirates ID and IBAN are ready, please " +
                "complete your Monthly Bank Payment Form by clicking on “Sign Now” below.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(
                    notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@,\n" +
                "We just want to remind you that for future months, a Monthly Bank Payment Form will be " +
                "needed. Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank " +
                "Payment Form by clicking on the following link: @link_send_dd_details@");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 3, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 10) {
            ddMessaging.setTrials("4");
            ddMessaging.setActive(true);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            if (!ddMessaging.getSendPayTabMessage()) {
                notificationTemplate.setText("Your payment covers your maid's service until @paid_end_date@ but " +
                    "you still didn't set up a Monthly Bank Payment Form. We just want to remind you " +
                    "that for future months, a Monthly Bank Payment Form will be needed. If we don’t receive " +
                    "your payment by @paid_end_date - 1@, your maid's service will be automatically " +
                    "cancelled and your maid will be notified to leave your home. Please whenever your " +
                    "Emirates ID and IBAN are ready, would you be so kind as to complete your Monthly " +
                    "Bank Payment Form by clicking on \"Sign Now\" below.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@\n" +
                    "Your payment covers your maid's service until @paid_end_date@. We just want to remind you " +
                    "that for future months, a Monthly Bank Payment Form will be needed. Please whenever " +
                    "your Emirates ID and IBAN are ready, would you be so kind as to complete your " +
                    "Monthly Bank Payment Form by clicking on the following link: " +
                    "@link_send_dd_details@ \n" +
                    "If we don’t receive your payment, your maid's service with maids.cc will be " +
                    "terminated and your maid will have to leave your home on @scheduled_termination_date@.");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            } else {
                notificationTemplate.setText("We're still waiting for you to complete your Monthly Bank " +
                    "Payment Form. Whenever your Emirates ID and IBAN are ready, " +
                    "please click \"Sign Now\" below to complete your form. If you still don't have " +
                    "your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card by " +
                    "clicking on \"Pay By Credit/Debit Card\" below.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@\n" +
                    "We're still waiting for you to complete your Monthly Bank Payment Form. Whenever " +
                    "your Emirates ID and IBAN are ready, please complete your Monthly Bank Payment Form " +
                    "by clicking on the following link: @link_send_dd_details@ \n" +
                    "If you still don't have your Emirates ID and IBAN ready, we can exceptionally " +
                    "accept your payment by credit or debit card using the following link. @paytabs_link@");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            }

            logger.log(Level.INFO, "dd trial: 4, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 15) {
            ddMessaging.setTrials("5");
            ddMessaging.setActive(true);
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            if (!ddMessaging.getSendPayTabMessage()) {
                notificationTemplate.setText("We're still waiting for you to complete your Monthly Bank " +
                    "Payment Form. Please whenever your Emirates ID and IBAN are ready, please click " +
                    "\"Sign Now\" below to complete your form.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@\n" +
                    "We're still waiting for you to complete your Monthly Bank Payment Form. Please " +
                    "whenever your Emirates ID and IBAN are ready, would you be so kind as to complete " +
                    "it by clicking on the following link: @link_send_dd_details@");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            } else {
                notificationTemplate.setText("We're still waiting for you to complete your Monthly Bank " +
                    "Payment Form. Whenever your Emirates ID and IBAN are ready, please click " +
                    "\"Sign Now\" below to complete your form. If you still don't have your Emirates ID and " +
                    "IBAN ready, we can exceptionally accept your payment by credit or debit card by " +
                    "clicking on \"Pay By Credit/Debit Card\" below.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@\n" +
                    "We're still waiting for you to complete your Monthly Bank Payment Form. Please " +
                    "whenever your Emirates ID and IBAN are ready, would you be so kind as to complete " +
                    "it by clicking on the following link: @link_send_dd_details@ \n" +
                    "If you still don't have your Emirates ID and IBAN ready, we can exceptionally " +
                    "accept your payment by credit or debit card using the following link. @paytabs_link@");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            }

            logger.log(Level.INFO, "dd trial: 5, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 20) {
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(true);
            ddMessaging.setTrials("6");
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("Your payment covers your maid's service until @paid_end_date@. To " +
                "extend the service for 1 more month, we can exceptionally accept your payment by debit " +
                "or credit card by clicking on \"Pay By Credit/Debit Card\" below.\n" +
                "If we don’t receive your payment by @paid_end_date - 1@, your maid's service will be " +
                "automatically cancelled and your maid will be notified to leave your home.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(
                    notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("Your payment covers your maid's service until @paid_end_date@. To extend the " +
                "service for 1 more month, we can exceptionally accept your payment by debit or credit " +
                "card using the following link: @paytabs_link@ .\n" +
                "If we don’t receive your payment, your maid's service with maids.cc will be terminated " +
                "and your maid will have to leave your home on @scheduled_termination_date@");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 6, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 22) {
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(true);
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("7");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("Your payment covers your maid's service until @paid_end_date@. To " +
                "extend the service for 1 more month, we can exceptionally accept your payment by debit " +
                "or credit card by clicking on \"Pay By Credit/Debit Card\" below.\n" +
                "If we don’t receive your payment by @paid_end_date - 1@, your maid's service will be " +
                "automatically cancelled and your maid will be notified to leave your home.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(
                    notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@\n" +
                "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 more " +
                "month, we can exceptionally accept your payment by debit or credit card by using the " +
                "following link: @paytabs_link@ .\n" +
                "If we don’t receive your payment  by @paid_end_date - 1@, your maid's service will be " +
                "automatically cancelled and your maid will be notified to leave your home.");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 7, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 23) {
            ddMessaging.setCreateHumanSms(true);
            ddMessaging.setHumanSmsTitle(null);
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(true);
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("8");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("Your payment covers your maid's service until @paid_end_date@. To " +
                "extend the service for 1 more month, we can exceptionally accept your payment by debit " +
                "or credit card by clicking on \"Pay By Credit/Debit Card\" below.\n" +
                "If we don’t receive your payment by @paid_end_date - 1@, your maid's service will be " +
                "automatically cancelled and your maid will be notified to leave your home.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(
                    notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@\n" +
                "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 more " +
                "month, we can exceptionally accept your payment by debit or credit card by using the " +
                "following link: @paytabs_link@ .\n" +
                "If we don’t receive your payment by @paid_end_date - 1@, your maid's service will be " +
                "automatically cancelled and your maid will be notified to leave your home.");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 8, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 25) {
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("9");
            ddMessaging.setSendToMaid(true);
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(false);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            if (ddMessaging.getScheduleTermCategory().equals(DirectDebitMessagingScheduleTermCategory.EToday)) {
                notificationTemplate.setText("Sadly, our system has automatically terminated your " +
                    "service and notified your maid to leave your home since we didn't receive your Monthly " +
                    "Bank Payment Forms. We'll send an Uber today at 7pm to pick up @maid_first_name@ from your home.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@,\n" +
                    "Sadly, our system has automatically terminated your service and notified your maid " +
                    "to leave your home since we didn't receive your Monthly Bank Payment Forms. We'll " +
                    "send an Uber today at 7pm to pick up @maid_first_name@ from your home.");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());

                Template maidTemplate = ddMessaging.getMaidTemplate();
                maidTemplate.setText("@maid_first_name@, Your client's contract with maids.cc has been " +
                    "cancelled. We’ll order a taxi to pick you up today at 7pm and return you to " +
                    "the accommodation. Do not leave before we send you the taxi.");
                templateUtil.updateTemplate(maidTemplate, new HashMap<>());

                if (maidTemplate.getNotificationSmsTemplateName() != null) {
                    Template maidTemplateSms = templateRepository.findByNameIgnoreCase(
                            maidTemplate.getNotificationSmsTemplateName());
                    maidTemplateSms.setText("@maid_first_name@, Your client's contract with maids.cc has " +
                        "been cancelled. We’ll order a taxi to pick you up today at 7pm and " +
                        "return you to the accommodation. Do not leave before we send you the taxi.");
                    templateUtil.updateTemplate(maidTemplateSms, new HashMap<>());
                }

                logger.log(Level.INFO, "dd trial: 9 EToday, id {0}", ddMessaging.getId());
                return;
            } else if (ddMessaging.getScheduleTermCategory().equals(DirectDebitMessagingScheduleTermCategory.GToday)) {
                notificationTemplate.setText("Sadly, our system has automatically terminated your " +
                    "service and notified your maid to leave your home since we didn't receive your Monthly " +
                    "Bank Payment Forms. We'll send an Uber on @scheduled_termination_date@ at 7pm to " +
                    "pick up @maid_first_name@ from your home. ");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@,\n" +
                    "Sadly, our system has automatically terminated your service and notified your maid " +
                    "to leave your home since we didn't receive your Monthly Bank Payment Forms. We'll " +
                    "send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_first_name@ from your home.");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());

                Template maidTemplate = ddMessaging.getMaidTemplate();
                maidTemplate.setText("@maid_first_name@, Your client's contract with maids.cc has been " +
                    "cancelled. We’ll order a taxi to pick you up on @scheduled_termination_day@ at " +
                    "7pm and return you to the accommodation. Do not leave before we send you the taxi.");
                templateUtil.updateTemplate(maidTemplate, new HashMap<>());

                if (maidTemplate.getNotificationSmsTemplateName() != null) {
                    Template maidTemplateSms = templateRepository.findByNameIgnoreCase(
                            maidTemplate.getNotificationSmsTemplateName());
                    maidTemplateSms.setText("@maid_first_name@, Your client's contract with maids.cc has " +
                        "been cancelled. We’ll order a taxi to pick you up on " +
                        "@scheduled_termination_day@ at 7pm and return you to the accommodation. " +
                        "Do not leave before we send you the taxi.");
                    templateUtil.updateTemplate(maidTemplateSms, new HashMap<>());
                }

                logger.log(Level.INFO, "dd trial: 9 GToday, id {0}", ddMessaging.getId());
                return;
            }
        }

        logger.log(Level.INFO, "dd trial: not match any case, id {0}", ddMessaging.getId());
    }

    public void updateNoSignatureDdMessagingMvDirectlyOnContactCreation(DDMessaging ddMessaging) {
        logger.log(Level.INFO, "old dd id: {0}", ddMessaging.getId());

        Template notificationTemplate = ddMessaging.getClientTemplate();
        notificationTemplate.setText("Your payment covers your maid's service until @adjusted_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
            "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” to complete your Monthly Bank Payment Form.");
        templateUtil.updateTemplate(notificationTemplate, new HashMap<>());
        
        Template smsTemplateMv = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
        smsTemplateMv.setText("@greetings@, Your payment covers your maid's service until @adjusted_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
            "Please whenever your Emirates ID and IBAN are ready, " +
            "please complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@");
        templateUtil.updateTemplate(smsTemplateMv, new HashMap<>());

        ddMessaging.setTrials("1");
        ddMessaging.setActive(true);
    }

    public void updateNoSignatureDdMessagingMvFirstOfNextMonthOfContactCreation(DDMessaging ddMessaging) {
        logger.log(Level.INFO, "old dd id: {0}", ddMessaging.getId());

        if (ddMessaging.getAfterDays() == 0) {
            ddMessaging.setActive(true);
            ddMessaging.setTrials("2");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("We just want to remind you that for future months, a Monthly Bank Payment Form will be needed. " +
                "Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank Payment Form by clicking on “Sign Now” below.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@ , We just want to remind you that for future months, a Monthly Bank Payment Form will be needed. " +
                "Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 2, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 5) {
            ddMessaging.setTrials("3");
            ddMessaging.setActive(true);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("We just want to remind you that for future months, a Monthly Bank Payment Form will be needed. " +
                "Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank Payment Form by clicking on “Sign Now” below.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@, We just want to remind you that for future months, a Monthly Bank Payment Form will be needed. " +
                "Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 3, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 10) {
            ddMessaging.setTrials("4");
            ddMessaging.setActive(true);
            Template notificationTemplate = ddMessaging.getClientTemplate();

            if (!ddMessaging.getSendPayTabMessage()) {
                notificationTemplate.setText("Your payment covers your maid's service until @adjusted_end_date@. We just want to remind you that for future months, " +
                    "a Monthly Bank Payment Form will be needed. Please whenever your Emirates ID and IBAN are ready, would you be so kind as to " +
                    "complete your Monthly Bank Payment Form by clicking on “Sign Now” below. " +
                    "If we don’t receive your payment by @adjusted_end_date - 1@, your service and your maid's visa will be automatically cancelled. ");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@ Your payment covers your maid's service until @adjusted_end_date@. We just want to remind you that for future months, " +
                    "a Monthly Bank Payment Form will be needed. Please whenever your Emirates ID and IBAN are ready, " +
                    "would you be so kind as to complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@ ." +
                    "If we don’t receive your payment  by @adjusted_end_date - 1@, your service and your maid's visa will be automatically cancelled. ");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            } else {
                notificationTemplate.setText("We're still waiting for you to complete your Monthly Bank Payment Form. " +
                    "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” below to complete your form. " +
                    "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card by clicking " +
                    "on ”Pay By Credit/Debit Card” below.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. " +
                    "Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@ " +
                    "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment " +
                    "by credit or debit card using the following link. @paytabs_link@");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            }

            logger.log(Level.INFO, "dd trial: 4, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 15) {
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("5");
            ddMessaging.setActive(true);

            Template notificationTemplate = ddMessaging.getClientTemplate();
            if (!ddMessaging.getSendPayTabMessage()) {
                notificationTemplate.setText("We're still waiting for you to complete your Monthly Bank Payment Form. " +
                    "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” below to complete your form.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. " +
                    "Please whenever your Emirates ID and IBAN are ready, would you be so kind as to complete it by clicking on the following link: @link_send_dd_details@");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            } else {
                notificationTemplate.setText("We're still waiting for you to complete your Monthly Bank Payment Form. Please whenever your Emirates ID " +
                    "and IBAN are ready, please click ”Sign Now” below to complete your form. If you still don't have your Emirates ID and IBAN ready, " +
                    "we can exceptionally accept your payment by credit or debit card by clicking on ”Pay By Credit/Debit Card” below.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. " +
                    "Please whenever your Emirates ID and IBAN are ready, would you be so kind as to complete it by clicking on the following link: @link_send_dd_details@ " +
                    "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or " +
                    "debit card using the following link. @paytabs_link@");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());
            }

            logger.log(Level.INFO, "dd trial: 5, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 20) {
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(true);
            ddMessaging.setTrials("6");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("Your payment covers your maid's service until @adjusted_end_date@. To extend the service for 1 more month, " +
                "we can exceptionally accept your payment by debit or credit card by clicking on ”Pay By Credit/Debit Card” below. " +
                "If we don’t receive your payment  by @adjusted_end_date - 1@, your service and your maid's visa will be automatically cancelled.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("Your payment covers your maid's service until @adjusted_end_date@. To extend the service for 1 more month, " +
                "we can exceptionally accept your payment by debit or credit card by clicking on the following link: @paytabs_link@ " +
                "If we don’t receive your payment  by @adjusted_end_date - 1@, your service and your maid's visa will be automatically cancelled.");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 6, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 22) {
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("7");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("Your payment covers your maid's service until @adjusted_end_date@. " +
                "To extend the service for 1 more month, we can exceptionally accept your payment by debit or " +
                "credit card by clicking on ”Pay By Credit/Debit Card” below. " +
                "If we don’t receive your payment  by @adjusted_end_date - 1@, your service and your maid's visa will be automatically cancelled.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@, Your payment covers your maid's service until @adjusted_end_date@. " +
                "To extend the service for 1 more month, we can exceptionally accept your payment by debit " +
                "or credit card using the following link: @paytabs_link@ . " +
                "If we don’t receive your monthly bank payment form today, your service with maids.cc will be terminated and your maid's visa will be canceled.");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(true);

            logger.log(Level.INFO, "dd trial: 7, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 23) {
            /*ddMessaging.setCreateHumanSms(true);
            ddMessaging.setHumanSmsTitle(null);*/
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(true);
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("8");

            Template notificationTemplate = ddMessaging.getClientTemplate();
            notificationTemplate.setText("Your payment covers your maid's service until @adjusted_end_date@. To extend the service for 1 more month, " +
                "we can exceptionally accept your payment by debit or credit card by clicking on ”Pay By Credit/Debit Card” below. " +
                "if we don’t receive your monthly bank payment form today, your service with maids.cc will be terminated and your maid's visa will be canceled.");
            templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

            Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
            smsTemplate.setText("@greetings@\n" +
                "Your payment covers your maid's service until @adjusted_end_date@. To extend the service for 1 more month, " +
                "we can exceptionally accept your payment by debit or credit card using the following link: @paytabs_link@ .\n" +
                "If we don’t receive your monthly bank payment form today, your service with maids.cc will be terminated and your maid's visa will be canceled.");
            templateUtil.updateTemplate(smsTemplate, new HashMap<>());

            logger.log(Level.INFO, "dd trial: 8, id {0}", ddMessaging.getId());
            return;
        }

        if (ddMessaging.getAfterDays() == 25) {
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
            ddMessaging.setTrials("9");
            ddMessaging.setSendToMaid(true);
            ddMessaging.setActive(true);
            ddMessaging.setSendPayTabMessage(false);
            Template notificationTemplate = ddMessaging.getClientTemplate();

            if (ddMessaging.getScheduleTermCategory().equals(DirectDebitMessagingScheduleTermCategory.EToday)) {
                notificationTemplate.setText("Sadly, our system has automatically terminated your service and sent " +
                    "your maid an SMS with a link to sign her visa cancellation paper since we didn't receive your " +
                    "Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@, Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                    "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());

                Template maidTemplate = ddMessaging.getMaidTemplate();
                maidTemplate.setText("@maid_first_name@ , Your client's contract with maids.cc has been cancelled. " +
                    "Please click on the following link and sign your visa cancellation paper today @scheduled_termination_date@: @visa_cancellation_paper_link@ .");
                templateUtil.updateTemplate(maidTemplate, new HashMap<>());

                if (maidTemplate.getNotificationSmsTemplateName() != null) {
                    Template maidTemplateSms = templateRepository.findByNameIgnoreCase(maidTemplate.getNotificationSmsTemplateName());
                    maidTemplateSms.setText("@maid_first_name@ , Your client's contract with maids.cc has been cancelled. " +
                        "Please click on the following link and sign your visa cancellation paper today @scheduled_termination_date@: @visa_cancellation_paper_link@ .");
                    templateUtil.updateTemplate(maidTemplateSms, new HashMap<>());
                }

                logger.log(Level.INFO, "dd trial: 9 EToday, id {0}", ddMessaging.getId());
                return;
            }

            if (ddMessaging.getScheduleTermCategory().equals(DirectDebitMessagingScheduleTermCategory.GToday)) {
                notificationTemplate.setText("Sadly, our system has automatically terminated your service and sent your maid an SMS with a link to " +
                    "sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
                templateUtil.updateTemplate(notificationTemplate, new HashMap<>());

                Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                smsTemplate.setText("@greetings@, Sadly, our system has automatically terminated your service and " +
                    "sent your maid an SMS with a link to sign her visa cancellation paper since we didn't receive " +
                    "your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
                templateUtil.updateTemplate(smsTemplate, new HashMap<>());

                Template maidTemplate = ddMessaging.getMaidTemplate();
                maidTemplate.setText("@maid_first_name@ , Your client's contract with maids.cc has been cancelled. " +
                    "Please click on the following link and sign your visa cancellation paper before @scheduled_termination_date@: @visa_cancellation_paper_link@ .");
                templateUtil.updateTemplate(maidTemplate, new HashMap<>());

                if (maidTemplate.getNotificationSmsTemplateName() != null) {
                    Template maidTemplateSms = templateRepository.findByNameIgnoreCase(maidTemplate.getNotificationSmsTemplateName());
                    maidTemplateSms.setText("@maid_first_name@ , Your client's contract with maids.cc has been cancelled. " +
                        "Please click on the following link and sign your visa cancellation paper before @scheduled_termination_date@: @visa_cancellation_paper_link@ .");
                    templateUtil.updateTemplate(maidTemplateSms, new HashMap<>());
                }

                logger.log(Level.INFO, "dd trial: 9 GToday, id {0}", ddMessaging.getId());
                return;
            }
        }

        logger.log(Level.INFO, "dd trial: not match any case, id {0}", ddMessaging.getId());
    }

    public void createNewDdMessageForAfterCashFlow(){
        createNewDdMessageForAfterCashFlow("@greetings@, Your payment covers your maid's service until @paid_end_date@. " +
                        "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
                        "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” to complete your Monthly Bank Payment Form.",
                "@greetings@, Your payment covers your maid's service until @paid_end_date@. " +
                        "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
                        "Please whenever your Emirates ID and IBAN are ready, " +
                        "please complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@",
                "1", "maidvisa.ae_prospect", false, DirectDebitMessagingWhenToStartSending.DIRECTLY_ON_CONTACT_CREATION
                , ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS, 0L, DDMessagingSubType.NO_SIGNATURE);

        createNewDdMessageForAfterCashFlow("We're still waiting for you to complete your Monthly Bank Payment Form. " +
                        "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” below to complete your form. " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card by clicking " +
                        "on ”Pay By Credit/Debit Card” below.",
                "@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. Please whenever your Emirates ID and IBAN are ready, " +
                        "complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@ " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card " +
                        "using the following link. @paytabs_link@",
                "4", "maids.cc_prospect", true,
                DirectDebitMessagingWhenToStartSending.FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION,
                ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS, 10L, DDMessagingSubType.NO_SIGNATURE);

        createNewDdMessageForAfterCashFlow("We're still waiting for you to complete your Monthly Bank Payment Form. " +
                        "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” below to complete your form. " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card " +
                        "by clicking on ”Pay By Credit/Debit Card” below.",
                "@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. " +
                        "Please whenever your Emirates ID and IBAN are ready, would you be so kind as to complete it by clicking " +
                        "on the following link: @link_send_dd_details@ " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card using " +
                        "the following link. @paytabs_link@",
                "5", "maids.cc_prospect", true,
                DirectDebitMessagingWhenToStartSending.FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION,
                ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION, 15L, DDMessagingSubType.NO_SIGNATURE);

        createNewDdMessageForAfterCashFlow("We're still waiting for you to complete your Monthly Bank Payment Form. " +
                        "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” below to complete your form. " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or debit card by clicking " +
                        "on ”Pay By Credit/Debit Card” below.",
                "@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. " +
                        "Please whenever your Emirates ID and IBAN are ready, complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@ " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment " +
                        "by credit or debit card using the following link. @paytabs_link@",
                "4", "maidvisa.ae_prospect", true,
                DirectDebitMessagingWhenToStartSending.FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION,
                ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS, 10L, DDMessagingSubType.NO_SIGNATURE);


        createNewDdMessageForAfterCashFlow("We're still waiting for you to complete your Monthly Bank Payment Form. Please whenever your Emirates ID " +
                        "and IBAN are ready, please click ”Sign Now” below to complete your form. If you still don't have your Emirates ID and IBAN ready, " +
                        "we can exceptionally accept your payment by credit or debit card by clicking on ”Pay By Credit/Debit Card” below.",
                "@greetings@, We're still waiting for you to complete your Monthly Bank Payment Form. " +
                        "Please whenever your Emirates ID and IBAN are ready, would you be so kind as to complete it by clicking on the following link: @link_send_dd_details@ " +
                        "If you still don't have your Emirates ID and IBAN ready, we can exceptionally accept your payment by credit or " +
                        "debit card using the following link. @paytabs_link@",
                "5", "maidvisa.ae_prospect", true,
                DirectDebitMessagingWhenToStartSending.FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION,
                ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION, 15L, DDMessagingSubType.NO_SIGNATURE);
    }

    public void createNewDdMessageForAfterCashFlow(
            String notificationText, String smsText,
            String trials,
            String contractProspectTypes,
            boolean sendPayTabMessage,
            DirectDebitMessagingWhenToStartSending whenToStartSending,
            ClientMessagePriority clientMessagePriority,
            Long afterDays,
            DDMessagingSubType subType){
        
        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.ClientPaidCashAndNoSignatureProvided);
        ddMessaging.setSubType(subType);
        ddMessaging.setClientMessage(notificationText);
        ddMessaging.setClientMessagePriority(clientMessagePriority);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setParameterToUsed("@greetings@,@dear_receiver_name@,@link_send_dd_details@");
        ddMessaging.setReminders("1");
        ddMessaging.setTrials(trials);
        ddMessaging.setSendPayTabMessage(sendPayTabMessage);
        ddMessaging.setWhenToStartSending(whenToStartSending);
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setSendToSpouse(true);
        ddMessaging.setSendToMaid(false);
        ddMessaging.setSendToMaidWhenRetractCancellation(false);
        ddMessaging.setAfterDays(afterDays);

        ddMessagingRepository.save(ddMessaging);
        
        Template smsTemplate = templateRepository.findByNameIgnoreCase(ddMessaging.getClientTemplate().getNotificationSmsTemplateName());
        smsTemplate.setText(smsText);
        templateUtil.updateTemplate(smsTemplate, new HashMap<>());
    }
    
    //ACC-4909
    public void createNoSignatureOnePaymentOnlyMessage(List<DDMessaging> ddMessagingList) {
        List<DDMessaging> noSignatureOnePaymentOnlyMessaging =
                ddMessagingList.stream()
                        .filter(ddMessaging -> ddMessaging.getSubType()
                                .equals(DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY))
                        .collect(Collectors.toList());
        
        String ccNotification = "Your payment covers your maid's service until @paid_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
            "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” to complete your Monthly " +
            "Bank Payment Form.";
        String ccSms = "@greetings@, Your payment covers your maid's service until @paid_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
            "Please whenever your Emirates ID and IBAN are ready, please complete your Monthly Bank " +
            "Payment Form by clicking on the following link: @link_send_dd_details@";
        String mvNotification = "@greetings@, Your payment covers your maid's service until @paid_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
            "Please whenever your Emirates ID and IBAN are ready, please click ”Sign Now” to complete your Monthly " +
            "Bank Payment Form.";
        String mvSms = "@greetings@, Your payment covers your maid's service until @paid_end_date@. " +
            "Please note that for future months, a Monthly Bank Payment Form will be needed. " +
            "Please whenever your Emirates ID and IBAN are ready, " +
            "please complete your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@";
        
        if (noSignatureOnePaymentOnlyMessaging.isEmpty()) {
            createNewDdMessageForAfterCashFlow(ccNotification, ccSms,"1", "maids.cc_prospect",
                false, DirectDebitMessagingWhenToStartSending.DIRECTLY_ON_CONTACT_CREATION,
                ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS, 0L, DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY);
            
            createNewDdMessageForAfterCashFlow(mvNotification, mvSms,"1", "maidvisa.ae_prospect",
                false, DirectDebitMessagingWhenToStartSending.DIRECTLY_ON_CONTACT_CREATION,
                ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS, 0L, DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY);
        } else {
            noSignatureOnePaymentOnlyMessaging.forEach(ddMessaging -> {
                    ddMessaging.setParameterToUsed("@greetings@,@dear_receiver_name@,@link_send_dd_details@");
                    ddMessaging.setReminders("1");
                    ddMessaging.setSubType(DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY);
                    ddMessaging.setActive(true);
                    ddMessaging.setCreateHumanSms(false);
                    ddMessaging.setHumanSmsTitle(null);
                    ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
                    ddMessaging.setSendToMaid(false);
                    ddMessaging.setTrials("1");
                    
                    Template notificationTemplate = ddMessaging.getClientTemplate();
                    Template smsTemplate = templateRepository.findByNameIgnoreCase(notificationTemplate.getNotificationSmsTemplateName());
                    
                    if (ddMessaging.getContractProspectTypes().equals("maids.cc_prospect")) {
                        notificationTemplate.setText(ccNotification);
                        smsTemplate.setText(ccSms);
                    } else {
                        notificationTemplate.setText(mvNotification);
                        smsTemplate.setText(mvSms);
                    }
                    
                    templateUtil.updateTemplate(notificationTemplate, new HashMap<>());
                    templateUtil.updateTemplate(smsTemplate, new HashMap<>());
                    ddMessagingRepository.save(ddMessaging);
            });
        }
    }


    @PreAuthorize("hasPermission('flowProcessor','createAfterCashFlow')")
    @GetMapping("/createAfterCashFlow/{id}")
    public ResponseEntity<?> createAfterCashFlow(
            @PathVariable(name = "id") Contract contract) {

        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(
                FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        FlowSubEventConfig noSignatureSubEvent = flowSubEventConfigRepository
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flowEventConfig);


        Map<String, Object> flow = new HashMap<>();
        flow.put("trials", 0);
        flow.put("reminders", 1);
        flow.put("lastExecutionDate", new Date());

        FlowProcessorEntity entity = flowProcessorService.createFlowProcessor(
                flowEventConfig, noSignatureSubEvent,
                contractPaymentTerm, flow);

        flowProcessorService.processFlowSubEventConfig(entity);

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('flowProcessor','onlinePaymentsFlowSetup')")
    @GetMapping("/onlinePaymentsFlowSetup")
    @Transactional
    public ResponseEntity<?> onlinePaymentsFlowSetup() {

        FlowEventConfig flowEventConfig = new FlowEventConfig();
        flowEventConfig.setName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        flowEventConfig.setMaxFlowRuns(1);
        flowEventConfig = flowEventConfigRepository.save(flowEventConfig);

        FlowSubEventConfig flowSubEventConfig = new FlowSubEventConfig();
        flowSubEventConfig.setName(FlowSubEventConfig.FlowSubEventName.PENDING_PAYMENT);
        flowSubEventConfig.setFlowEventConfig(flowEventConfig);
        flowSubEventConfig.setRequiredAction(RequiredAction.ONLINE_CREDIT_CARD_PAYMENT_RECEIVED);
        flowSubEventConfig.setMaxTrials(1);
        flowSubEventConfig.setMaxReminders(5);
        flowSubEventConfig.setTerminateContractOnMaxReminders(false);
        flowSubEventConfig.setTerminateContractOnMaxTrials(false);
        flowSubEventConfig.setInfiniteReminder(true);
        flowSubEventConfig = flowSubEventConfigRepository.save(flowSubEventConfig);


        for (int index = 1; index < 6 ; index++) {
            FlowProgressPeriod flowProgressPeriod = new FlowProgressPeriod();
            flowProgressPeriod.setFlowSubEventConfig(flowSubEventConfig);
            flowProgressPeriod.setReminders(index);
            flowProgressPeriod.setTrials(1);
            flowProgressPeriod.setPeriodInHours(24);
            flowProgressPeriodRepository.save(flowProgressPeriod);
        }
        return okResponse();
    }

    @PreAuthorize("hasPermission('flowProcessor','OnlinePaymentsFlowNotificationsSetup')")
    @GetMapping("/OnlinePaymentsFlowNotificationsSetup")
    public ResponseEntity<?> OnlinePaymentsFlowNotificationsSetup() {

        OnlinePaymentsFlowNotificationsSetupCc();
        OnlinePaymentsFlowNotificationsSetupMv();

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    public void OnlinePaymentsFlowNotificationsSetupMv() {
        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We'd just like to remind you to settle your payment of AED @amount@ today by " +
            "clicking on Pay Now.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ We'd just like to remind you to settle your " +
            "payment of AED @amount@ today by clicking on the following payment link: @paytabs_link@");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "2"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We'd just like to remind you to settle your payment of AED @amount@ today by " +
            "clicking on Pay Now.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ We'd just like to remind you to settle your " +
            "payment of AED @amount@ today by clicking on the following payment link: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "3"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "You must settle your payment of AED @amount@ today to avoid any interruption " +
            "in your service. Please take a moment right now to click on the button below and pay by credit card.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ You must settle your payment of AED @amount@ " +
            "today to avoid any interruption in your service. Please take a moment right now to click on the " +
            "following payment link and pay by credit card: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "4"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "You must settle your payment of AED @amount@ today to avoid any interruption " +
            "in your service. Please take a moment right now to click on the button below and pay by credit card.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ You must settle your payment of AED @amount@ today to " +
            "avoid any interruption in your service. Please take a moment right now to click on the following " +
            "payment link and pay by credit card: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "5"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We're sure your maid is very valuable to you and your family. Unfortunately, " +
            "if you don't pay the required payment of AED @amount@ by today, our system will automatically cancel your " +
            "service and your maid's visa. To continue your service, please click on Pay Now and pay by credit card.");
        map.put("smsText", "@greetings@, @dear_receiver_name@. We're sure your maid is very valuable to you and your family. " +
            "Unfortunately, if you don't pay the required payment of AED @amount@ by today, our system will automatically " +
            "cancel your service and your maid's visa. To continue your service, please click on the following payment " +
            "link and pay by credit card: @paytabs_link@ .");

        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.OnlineCreditCardPaymentReminders, DDMessagingSubType.PENDING_PAYMENT);
    }

    public void OnlinePaymentsFlowNotificationsSetupCc() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We'd just like to remind you to settle your payment of AED @amount@ today by " +
            "clicking on Pay Now.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ We'd just like to remind you to settle your payment of " +
            "AED @amount@ today by clicking on the following payment link: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "2"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We'd just like to remind you to settle your payment of AED @amount@ today by " +
            "clicking on Pay Now.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ We'd just like to remind you to settle your payment " +
            "of AED @amount@ today by clicking on the following payment link: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "3"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "You must settle your payment of AED @amount@ today to avoid any interruption " +
            "in your service. Please take a moment right now to click on the button below and pay by credit card.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ You must settle your payment of AED @amount@ today to " +
            "avoid any interruption in your service. Please take a moment right now to click on the following payment " +
            "link and pay by credit card: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "4"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "You must settle your payment of AED @amount@ today to avoid any interruption " +
            "in your service. Please take a moment right now to click on the button below and pay by credit card.");
        map.put("smsText", "@greetings@, @dear_receiver_name@ You must settle your payment of AED @amount@ today to " +
            "avoid any interruption in your service. Please take a moment right now to click on the following payment " +
            "link and pay by credit card: @paytabs_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "5"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We're sure your maid is very valuable to you and your family. Unfortunately, " +
            "if you don't pay the required payment of AED @amount@ by today, our system will automatically cancel your " +
            "service. To continue your service, please press on Pay Now and pay by credit card.");
        map.put("smsText", "@greetings@, @dear_receiver_name@. We're sure your maid is very valuable to you and your " +
            "family. Unfortunately, if you don't pay the required payment of AED @amount@ by today, our system will " +
            "automatically cancel your service. To continue your service, please click on the following payment link " +
           "and pay by credit card: @paytabs_link@ .");

        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.OnlineCreditCardPaymentReminders, DDMessagingSubType.PENDING_PAYMENT);
    }

    private void createDdMessaging(
            List<Map<String, Object>> ddMessagingList,
            DDMessagingType event,
            DDMessagingSubType subType) {

        List<DDMessaging> oldDdMessagingList = ddMessagingRepository.findByEventAndIsActiveTrueAndSubType(
                event, subType);

        ddMessagingList.forEach(ddMessagingMap -> {
            DDMessaging ddMessaging = new DDMessaging();
            ddMessaging.setEvent(event);
            ddMessaging.setSubType(subType);
            ddMessaging.setTrials((String) ddMessagingMap.get("trials"));
            ddMessaging.setReminders((String) ddMessagingMap.get("reminders"));
            ddMessaging.setContractProspectTypes((String) ddMessagingMap.get("contractProspectTypes"));
            ddMessaging.setSendPayTabMessage(ddMessagingMap.get("sendPayTabMessage") != null
                && (Boolean) ddMessagingMap.get("sendPayTabMessage"));
            ddMessaging.setRejectCategory((DirectDebitRejectCategory) ddMessagingMap.get("rejectCategory"));
            ddMessaging.setScheduleTermCategory(ddMessagingMap.get("scheduleTermCategory") == null ?
                DirectDebitMessagingScheduleTermCategory.None
                : (DirectDebitMessagingScheduleTermCategory) ddMessagingMap.get("scheduleTermCategory"));

            logger.log(Level.INFO, "event: {0}; subType: {1}; trials: {2}; reminder: {3}; contractProspectType: {4};" +
                    " sendPayTab: {5}; scheduleTermCategory: {6}; rejectCategory: {7}",
                new Object[]{ddMessaging.getEvent(), ddMessaging.getSubType(), ddMessaging.getTrials(),
                    ddMessaging.getReminders(), ddMessaging.getContractProspectTypes(),
                    ddMessaging.getSendPayTabMessage(), ddMessaging.getScheduleTermCategory(), ddMessaging.getRejectCategory()});
            ClientMessagePriority clientMessagePriority = (ClientMessagePriority) ddMessagingMap.get("clientMessagePriority");
            String notificationText = (String) ddMessagingMap.get("notificationText");
            String smsText = (String) ddMessagingMap.get("smsText");
            String maidText = (String) ddMessagingMap.get("maidText");

            DDMessaging oldDd = oldDdMessagingList.stream().filter(dd ->
                    dd.getEvent().equals(ddMessaging.getEvent()) &&
                        dd.getSubType().equals(ddMessaging.getSubType()) &&
                        dd.getTrials().equals(ddMessaging.getTrials()) &&
                        dd.getReminders().equals(ddMessaging.getReminders()) &&
                        dd.getSendPayTabMessage().equals(ddMessaging.getSendPayTabMessage()) &&
                        dd.getContractProspectTypes().equals(ddMessaging.getContractProspectTypes()) &&
                        dd.getScheduleTermCategory().equals(ddMessaging.getScheduleTermCategory())
                        && (dd.getRejectCategory() == null ||
                        dd.getRejectCategory().equals(ddMessaging.getRejectCategory())))
                .findFirst().orElse(null);

            if (oldDd != null) {
                logger.log(Level.INFO, "oldDd id: {0}", oldDd.getId());
                oldDd.setClientMessagePriority(clientMessagePriority);
                oldDd.setSendAsEmail(false);
                oldDd.setSendToClient(true);
                oldDd.setSendToSpouse(true);
                oldDd.setSendToMaid(false);
                oldDd.setSendToMaidWhenRetractCancellation(false);
                oldDd.setScheduleTermCategory(ddMessaging.getScheduleTermCategory());
                oldDd.setRejectCategory(ddMessaging.getRejectCategory());
                oldDd.setContractProspectTypes(ddMessaging.getContractProspectTypes());

                Template t = oldDd.getClientTemplate();
                t.getChannelSetting(ChannelSpecificSettingType.Notification.toString()).setText(notificationText);
                t.getChannelSetting(ChannelSpecificSettingType.SMS.toString()).setText(smsText);
                Setup.getApplicationContext()
                        .getBean(TemplateUtil.class)
                        .updateTemplate(t, new HashMap<>());

                if (maidText != null) {
                    if (oldDd.getMaidTemplate() != null) {
                        Template maidTemplate = oldDd.getMaidTemplate();
                        maidTemplate.setText(maidText);
                        Setup.getApplicationContext().getBean(TemplateUtil.class)
                                .updateTemplate(maidTemplate, new HashMap<>());
                        Template maidSmsTemplate = templateRepository.findByNameIgnoreCase(
                                maidTemplate.getNotificationSmsTemplateName());
                        maidSmsTemplate.setText(maidText);
                        Setup.getApplicationContext().getBean(TemplateUtil.class)
                                .updateTemplate(maidSmsTemplate, new HashMap<>());
                    } else {
                        oldDd.setMaidMessage(maidText);
                    }
                    oldDd.setSendToMaid(true);
                }
                ddMessagingRepository.save(oldDd);

            } else {
                ddMessaging.setClientMessagePriority(clientMessagePriority);
                ddMessaging.setSendAsEmail(false);
                ddMessaging.setSendToClient(true);
                ddMessaging.setSendToSpouse(true);
                ddMessaging.setSendToMaid(false);
                ddMessaging.setSendToMaidWhenRetractCancellation(false);
                ddMessaging.setClientMessage(notificationText);

                if (maidText != null) {
                    ddMessaging.setMaidMessage(maidText);
                    ddMessaging.setSendToMaid(true);
                }
                ddMessagingRepository.save(ddMessaging);
                logger.log(Level.INFO, "ddMessaging id: {0}", ddMessaging.getId());
            }
        });
    }
    //ACC-4715
    @PreAuthorize("hasPermission('flowProcessor','clientPayingViaCreditCardFlowSetup')")
    @Transactional
    @GetMapping("/clientPayingViaCreditCardFlowSetup")
    public ResponseEntity<?> clientPayingViaCreditCardFlowSetup() {
        if (tagRepository.findByNameIgnoreCase("day_of_send_dd_signing_offer").isEmpty()) {
            Tag tag1 = new Tag();
            tag1.setName("day_of_send_dd_signing_offer");
            tagRepository.save(tag1);
        }

        if (tagRepository.findByNameIgnoreCase("monthly_reminder_paying_cc_start_before_x_days").isEmpty()) {
            Tag tag2 = new Tag();
            tag2.setName("monthly_reminder_paying_cc_start_before_x_days");
            tagRepository.save(tag2);
        }

        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("stopMessagesOnContractCancellation", true);
        eventMap.put("stopTodosOnContractCancellation", true);
        eventMap.put("closeToDosUponCompletion", true);
        eventMap.put("maxFlowRuns", 1);
        eventMap.put("cancellationReason", flowProcessorService.createOrFetchCancellationReason(
                Contract.DUE_PAYING_VIA_CC_FLOW_TERMINATION_REASON, "Due Paying via CC Flow"));
        FlowEventConfig flowEventConfig = flowProcessorService.createOrFetchFlowEventConfig(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card, eventMap);

        Map<String, Object> subEventMap = new HashMap<>();
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB);
        subEventMap.put("requiredAction", RequiredAction.ONLINE_CREDIT_CARD_PAYMENT_RECEIVED);
        subEventMap.put("maxTrials", 4);
        subEventMap.put("maxReminders", 1);
        subEventMap.put("terminateOnMaxTrials", true);
        subEventMap.put("terminateOnMaxReminders", false);
        FlowSubEventConfig flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        List<Map<String, Integer>> values = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 2); map.put("reminder", 1); map.put("period", 48); values.add(map); map = new HashMap<>();
        map.put("trial", 3); map.put("reminder", 1); map.put("period", 48); values.add(map); map = new HashMap<>();
        map.put("trial", 4); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig); values = new ArrayList<>();

        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.DD_Rejection);
        subEventMap.put("maxTrials", 1);
        subEventMap.put("terminateOnMaxTrials", false);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.DD_SIGNING_OFFER);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        return okResponse();
    }
    //ACC-4715
    @PreAuthorize("hasPermission('flowProcessor','clientsPayingViaCreditCardFlowDdMessagingSetup')")
    @GetMapping("/clientsPayingViaCreditCardFlowDdMessagingSetup")
    public ResponseEntity<?> clientsPayingViaCreditCardFlowDdMessagingSetup() {

        clientsPayingViaCreditCardFlowDdMessagingSetupCc();
        clientsPayingViaCreditCardFlowDdMessagingSetupMv();

        return okResponse();
    }

    public void clientsPayingViaCreditCardFlowDdMessagingSetupCc() {
        //DDB_REJECTED
        clientsPayingViaCreditCardFlowDdMessagingSetupDdbRejectedCc();
        //DDA_REJECTED
        clientsPayingViaCreditCardFlowDdMessagingSetupDdaRejectedCc();
        //Rejected_DD_Submitted
        clientsPayingViaCreditCardFlowDdMessagingSetupRejectedDdSubmittedCc();
        //MONTHLY_REMINDER
        clientsPayingViaCreditCardFlowDdMessagingSetupMonthlyReminderCc();
        //DD_SIGNING_OFFER
        clientsPayingViaCreditCardFlowDdMessagingSetupDdSigningOfferCc();
    }

    public void clientsPayingViaCreditCardFlowDdMessagingSetupMv() {
        //DDB_REJECTED
        clientsPayingViaCreditCardFlowDdMessagingSetupDdbRejectedMv();
        //DDA_REJECTED
        clientsPayingViaCreditCardFlowDdMessagingSetupDdaRejectedMv();
        //Rejected_DD_Submitted
        clientsPayingViaCreditCardFlowDdMessagingSetupRejectedDdSubmittedMv();
        //MONTHLY_REMINDER
        clientsPayingViaCreditCardFlowDdMessagingSetupMonthlyReminderMv();
        //DD_SIGNING_OFFER
        clientsPayingViaCreditCardFlowDdMessagingSetupDdSigningOfferMv();
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupDdbRejectedCc() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your bank keeps rejecting your signature so we're not able to get an approved " +
                "Monthly Bank Payment Form so we can deduct your monthly payments. If we don’t receive your payment " +
                "by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be " +
                "notified to leave your home. To avoid this, please settle your monthly payment by @paying_via_credit_card_clicking_here@.");
        map.put("smsText", "@greetings@ Your bank keeps rejecting your signature so we're not able to get an approved " +
                "Monthly Bank Payment Form so we can deduct your monthly payments. If we don’t receive your payment  " +
                "by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified " +
                "to leave your home. To avoid this, please settle your monthly payment by credit or debit card by clicking on the following link: @paying_via_credit_card_sms@");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "2"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit card by @paying_via_credit_card_clicking_here@. " +
                "If we don’t receive your payment  by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your monthly payment by credit or debit card by clicking on the following link: @paying_via_credit_card_sms@ " +
                "If we don’t receive your payment  by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "3"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit card by @paying_via_credit_card_clicking_here@. " +
                "If we don’t receive your payment  by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your monthly payment by credit or debit card by clicking on the following link: @paying_via_credit_card_sms@ " +
                "If we don’t receive your payment  by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.EToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and notified your maid to leave your home since we didn't " +
                "receive your monthly payment. We'll send an Uber today at 7pm to pick up @maid_name@ from your home.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and notified your maid to leave your home since we didn't receive your monthly payment." +
                "We'll send an Uber today at 7pm to pick up @maid_name@ from your home.");
        map.put("maidText", "@maid_first_name@ , Your client's contract with maids.cc has been cancelled. We’ll order a taxi to pick you up today at 7pm " +
                "and return you to the accommodation. Do not leave before we send you the taxi.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.GToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and notified your maid to leave your home since we " +
                "didn't receive your monthly payment. We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_name@ from your home.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and notified your maid to leave your home since we didn't " +
                "receive your monthly payment. We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_name@ from your home.");
        map.put("maidText", "@maid_first_name@ , Your client's contract with maids.cc has been cancelled. We’ll order a taxi to pick you up on @scheduled_termination_date@ " +
                "at 7pm and return you to the accommodation. Do not leave before we send you the taxi.");
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.INITIAL_FLOW_FOR_DDB);

    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupDdaRejectedCc() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your bank keeps rejecting your signature so we're not able to get an approved Bank " +
                "Payment Form so we can deduct your payment. If we don’t receive your payment, your maid's service will " +
                "be automatically cancelled and your maid will be notified to leave your home. To avoid this, please " +
                "settle your payment of AED @amount@ by credit or debit card by @paying_via_credit_card_clicking_here@.");
        map.put("smsText", "@greetings@ Your bank keeps rejecting your signature so we're not able to get an approved " +
                "Bank Payment Form to deduct your payment. If we don’t receive your payment, your " +
                "maid's service will be automatically cancelled and your maid will be notified to " +
                "leave your home. To avoid this, please settle your payment of AED @amount@ by " +
                "credit or debit card by clicking on the following link: @paying_via_credit_card_sms@");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "2"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card, please @paying_via_credit_card_click_here@ to pay. If we don’t receive your payment, Our system will terminate" +
                "your maid's service with maids.cc and your maid will have to leave your home on @scheduled_termination_date@.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card, please click on the following link to pay @paying_via_credit_card_sms@ If we don’t receive your payment, " +
                "our system will automatically terminate your service and your maid will have to leave your home on @scheduled_termination_date@.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "3"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card, please @paying_via_credit_card_click_here@ to pay. If we don’t receive your payment, Our system will " +
                "terminate your maid's service with maids.cc and your maid will have to leave your home on @scheduled_termination_date@.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card by clicking on the following link:@paying_via_credit_card_sms@ If we don’t receive your payment, our system will " +
                "automatically terminate your service and your maid will have to leave your home on @scheduled_termination_date@.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.EToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and notified " +
                "your maid to leave your home since we didn't receive your monthly payment. We'll send an Uber today at " +
                "7pm to pick up @maid_name@ from your home.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and notified your maid to leave " +
                "your home since we didn't receive your monthly payment. We'll send an Uber today at 7pm to pick up " +
                "@maid_name@ from your home.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. We’ll order a taxi to " +
                "pick you up today at 7PM and return you to the accommodation. Do not leave before we send you the taxi.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.GToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and notified your " +
                "maid to leave your home since we didn't receive your monthly payment. We'll send an Uber on " +
                "@scheduled_termination_date@ at 7pm to pick up @maid_name@ from your home.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and notified your maid to " +
                "leave your home since we didn't receive your monthly payment. We'll send an Uber on  " +
                "@scheduled_termination_date@ at 7pm to pick up @maid_name@ from your home.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. " +
                "We’ll order a taxi to pick you up on @scheduled_termination_date@ at 7PM and return you to the " +
                "accommodation. Do not leave before we send you the taxi.");
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.INITIAL_FLOW_FOR_DDA);
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupRejectedDdSubmittedCc() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        //MV && CC
        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your Monthly Bank Payment Form was rejected by the bank due to incorrect " +
                "signature. No problem, you can continue paying your monthly payments by credit/debit card. Whenever " +
                "you’d like to try setting up your Monthly Payment Form again, please " +
                "@paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ Your Monthly Bank Payment Form was rejected by the bank due to an incorrect " +
                "signature. No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d " +
                "like to try setting up your Monthly Payment Form again, please visit the section of your maids.cc app " +
                "that’s called “Set up automatic monthly bank payment forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.Signature);
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "It looks like there was a mistake in the Emirates ID that you've submitted. " +
                "No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d like to " +
                "try setting up your Monthly Payment Form again, please @paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ It looks like there was a mistake in the Emirates ID that you've submitted. " +
                "No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d like to " +
                "try setting up your Monthly Payment Form again, please visit the section of your maids.cc app that’s " +
                "called “Set up automatic monthly bank payment forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.EID);
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1");
        map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "It looks like the name on the Emirates ID you sent us doesn't exactly match the " +
                "name of the bank account holder associated with the IBAN. No problem, you can continue paying your monthly " +
                "payments by credit/debit card. Whenever you’d like to try setting up your Monthly Payment Form again, " +
                "please @paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ It looks like the name on the Emirates ID you sent us doesn't exactly match " +
                "the name of the bank account holder associated with the IBAN. No problem, you can continue paying your " +
                "monthly payments by credit/debit card. Whenever you’d like to try setting up your Monthly Payment Form " +
                "again, please visit the section of your maids.cc app that’s called “Set up automatic monthly bank payment forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.Account);
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1");
        map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "It looks like the currency linked to the IBAN that you previously sent us is not AED. " +
                "No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d like to try " +
                "setting up your Monthly Payment Form again, please @paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ It looks like there was a mistake in the bank account holder name that you’ve" +
                " submitted. No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d " +
                "like to try setting up your Monthly Payment Form again, please visit the section of your maids.cc app " +
                "that’s called “Set up automatic monthly bank payment forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.Invalid_Account);
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.DD_Rejection);
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupMonthlyReminderCc() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 " +
                "more month, please settle the payment Today by credit or debit card by " +
                "@paying_via_credit_card_clicking_here@.");
        map.put("smsText", "@greetings@ Your payment covers your maid's service until @paid_end_date@. To extend the service " +
                "for 1 more month, please settle the payment Today by credit or debit card using the following link: " +
                "@paying_via_credit_card_sms@");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "2"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit " +
                "card by @paying_via_credit_card_clicking_here@. If we don’t receive your payment  by @paid_end_date - 1@," +
                " your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your monthly payment by credit or debit " +
                "card by clicking on the following link: @paying_via_credit_card_sms@ If we don’t receive your payment  by " +
                "@paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "3"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit " +
                "card by @paying_via_credit_card_clicking_here@. If we don’t receive your payment  by @paid_end_date - 1@, your maid's service will" +
                " be automatically cancelled and your maid will be notified to leave your home.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your monthly payment by credit or debit " +
                "card by clicking on the following link: @paying_via_credit_card_sms@ If we don’t receive your payment " +
                "by @paid_end_date - 1@, your maid's service will be automatically cancelled and your maid will be notified to leave your home.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.EToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and notified " +
                "your maid to leave your home since we didn't receive your monthly payment. We'll send an Uber today at 7pm" +
                "to pick up @maid_name@ from your home.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and notified your " +
                "maid to leave your home since we didn't receive your monthly payment. We'll send an Uber today at 7pm to " +
                "pick up @maid_name@ from your home.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. We’ll " +
                "order a taxi to pick you up today at 7PM and return you to the accommodation. Do not leave before we " +
                "send you the taxi.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.GToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and notified " +
                "your maid to leave your home since we didn't receive your monthly payment. We'll send an Uber on " +
                "@scheduled_termination_date@ at 7pm to pick up @maid_name@ from your home.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and notified your " +
                "maid to leave your home since we didn't receive your monthly payment. We'll send an Uber on " +
                "@scheduled_termination_date@ at 7pm to pick up @maid_name@ from your home.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. " +
                "We’ll order a taxi to pick you up on @scheduled_termination_date@ at 7PM and return you to the " +
                "accommodation. Do not leave before we send you the taxi.");
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.MONTHLY_REMINDER);
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupDdSigningOfferCc() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maids.cc_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.DO_NOT_SEND_SMS);
        map.put("notificationText", "You're currently paying your monthly payments by credit card. We just want to " +
                "let you know that you can always set up automatic payments through your personalized maids.cc app by @signing_offer_clicking_here@ .");
        map.put("smsText", "@greetings@,\n" +
                "You're currently paying your monthly payments by credit card. We just want to let you know that " +
                "you can set up automatic payments through your personalized maids.cc app by clicking on My " +
                "Payments and then on Add an Automatic Monthly Bank Payment Form. @cc_app_download_url@");
        ddMessagingList.add(map);

        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.DD_SIGNING_OFFER);
    }

    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupDdbRejectedMv() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your bank keeps rejecting your signature so we're not able to get an approved Monthly Bank Payment Form " +
                "so we can deduct your monthly payments. If we don’t receive your payment by @paid_end_date - 1@, your " +
                "service with maids.cc will be terminated and your maid's visa will be canceled. To avoid this, please " +
                "settle your monthly payment by @paying_via_credit_card_clicking_here@.");
        map.put("smsText", "@greetings@\nYour bank keeps rejecting your signature so we're not able to get an approved Monthly Bank Payment Form " +
                "so we can deduct your monthly payments. If we don’t receive your payment by @paid_end_date - 1@, your " +
                "service with maids.cc will be terminated and your maid's visa will be canceled. To avoid this, please " +
                "settle your monthly payment by credit or debit card by clicking on the following link: " +
                "@paying_via_credit_card_sms@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "2"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit " +
                "card by @paying_via_credit_card_clicking_here@.\n" +
                "If we don’t receive your payment by @paid_end_date - 1@, your service with maids.cc will be terminated " +
                "and your maid's visa will be canceled.");
        map.put("smsText", "@greetings@\n" +
                "We just want to remind you to settle your monthly payment by credit or debit card by clicking on the " +
                "following link: @paying_via_credit_card_sms@ . If we don’t receive your payment by @paid_end_date - 1@, " +
                "your service with maids.cc will be terminated and your maid's visa will be canceled.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "3"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit card by @paying_via_credit_card_clicking_here@. " +
                "If we don’t receive your payment by @paid_end_date - 1@, your service with maids.cc will be terminated and your maid's visa will be canceled.");
        map.put("smsText", "@greetings@\n" +
                "We just want to remind you to settle your monthly payment by credit or debit card by clicking on the " +
                "following link: @paying_via_credit_card_sms@ . If we don’t receive your payment by @paid_end_date - 1@, " +
                "your service with maids" +
                ".cc will be terminated and your maid's visa will be canceled.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_NEXT_DAY);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.EToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and sent your maid an SMS " +
                "with a link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. " +
                "Please click on the following link and sign your visa cancellation paper today " +
                "@scheduled_termination_date@: @visa_cancellation_paper_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_NEXT_DAY);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.GToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and sent your maid an SMS " +
                "with a link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. Please ask your maid to sign her visa cancellation paper today.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. " +
                "Please click on the following link and sign your visa cancellation paper today " +
                "@scheduled_termination_date@: @visa_cancellation_paper_link@ .");
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.INITIAL_FLOW_FOR_DDB);
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupDdaRejectedMv() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your bank keeps rejecting your signature so we're not able to get an approved Bank " +
                "Payment Form to deduct your monthly payments. If we don’t receive your payment, your service and your " +
                "maid's visa will be automatically. To avoid this, please settle your payment of AED @amount@ by credit " +
                "or debit card by @paying_via_credit_card_clicking_here@.");
        map.put("smsText", "@greetings@ Your bank keeps rejecting your signature so we're not able to get an approved Bank Payment Form to deduct" +
                " your monthly payments. If we don’t receive your payment, your service and your maid's visa will be " +
                "automatically. To avoid this, please settle your payment of AED @amount@ by credit or debit card by " +
                "clicking on the following link: @paying_via_credit_card_sms@");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "2"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card. Please @paying_via_credit_card_click_here@ to pay. If we don’t receive your payment, your service and your maid's " +
                "visa will automatically be cancelled.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card, please click here to pay @paying_via_credit_card_sms@ If we don’t receive your payment, " +
                "your service and your maid's visa will automatically be cancelled.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "3"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card. Please @paying_via_credit_card_click_here@ to pay. If we don’t receive your payment, your service and your maid's " +
                "visa will automatically be cancelled.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your overdue payment of AED @amount@ by " +
                "credit card, please click here to pay @paying_via_credit_card_sms@ If we don’t receive your payment, your service and maid's " +
                "visa will automatically be cancelled.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.EToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and sent your maid an SMS " +
                "with a link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. Please click " +
                "on the following link and sign your visa cancellation paper today @scheduled_termination_date@: " +
                "@visa_cancellation_paper_link@ .");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.GToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and sent your maid an SMS " +
                "with a link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. Please click " +
                "on the following link and sign your visa cancellation paper today @scheduled_termination_date@: " +
                "@visa_cancellation_paper_link@ .");
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.INITIAL_FLOW_FOR_DDA);
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupRejectedDdSubmittedMv() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your Monthly Bank Payment Form was rejected by the bank due to incorrect " +
                "signature. No problem, you can continue paying your monthly payments by credit/debit card. Whenever " +
                "you’d like to try setting up your Monthly Payment Form again, please " +
                "@paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ Your Monthly Bank Payment Form was rejected by the bank due to an incorrect " +
                "signature. No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d " +
                "like to try setting up your Monthly Payment Form again, please visit the section of your maids.cc app " +
                "that’s called “Set up automatic monthly Bank Payment Forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.Signature);
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "It looks like there was a mistake in the Emirates ID that you've submitted. " +
                "No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d like to " +
                "try setting up your Monthly Payment Form again, please @paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ It looks like there was a mistake in the Emirates ID that you've submitted. " +
                "No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d like to " +
                "try setting up your Monthly Payment Form again, please visit the section of your maids.cc app that’s " +
                "called “Set up automatic monthly Bank Payment Forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.EID);
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1");
        map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "It looks like the name on the Emirates ID you sent us doesn't exactly match the " +
                "name of the bank account holder associated with the IBAN. No problem, you can continue paying your monthly " +
                "payments by credit/debit card. Whenever you’d like to try setting up your Monthly Payment Form again, " +
                "please @paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ It looks like the name on the Emirates ID you sent us doesn't exactly match " +
                "the name of the bank account holder associated with the IBAN. No problem, you can continue paying your " +
                "monthly payments by credit/debit card. Whenever you’d like to try setting up your Monthly Payment Form " +
                "again, please visit the section of your maids.cc app that’s called “Set up automatic monthly Bank Payment Forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.Account);
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1");
        map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "It looks like the currency linked to the IBAN that you previously sent us is not AED. " +
                "No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d like to try " +
                "setting up your Monthly Payment Form again, please @paying_via_credit_card_sign_now_click_here@.");
        map.put("smsText", "@greetings@ It looks like there was a mistake in the bank account holder name that you’ve" +
                " submitted. No problem, you can continue paying your monthly payments by credit/debit card. Whenever you’d " +
                "like to try setting up your Monthly Payment Form again, please visit the section of your maids.cc app " +
                "that’s called “Set up automatic monthly Bank Payment Forms.");
        map.put("rejectCategory", DirectDebitRejectCategory.Invalid_Account);
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.DD_Rejection);
    }
    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupMonthlyReminderMv() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("notificationText", "Your payment covers your maid's visa service until @paid_end_date@. To extend the service for 1 more month, " +
                "please settle the payment Today by credit or debit card by @paying_via_credit_card_clicking_here@.");
        map.put("smsText", "@greetings@ Your payment covers your maid's visa service until @paid_end_date@. To extend the " +
                "maid's visa service for 1 more month, please settle the payment Today by credit or debit card using the " +
                "following link: @paying_via_credit_card_sms@");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "2"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit card by " +
                "@paying_via_credit_card_clicking_here@. If we don’t receive your payment  by @paid_end_date - 1@, your " +
                "service with maids.cc will be terminated and your maid's visa will be canceled.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your monthly payment by credit or debit " +
                "card by clicking on the following link: @paying_via_credit_card_sms@ If we don’t receive your payment  by @paid_end_date - 1@," +
                " your service with maids.cc will be terminated and your maid's visa will be canceled.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "3"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        map.put("notificationText", "We just want to remind you to settle your monthly payment by credit or debit card " +
                "by @paying_via_credit_card_clicking_here@. If we don’t receive your payment  by @paid_end_date - 1@, " +
                "your service with maids.cc will be terminated and your maid's visa will be canceled.");
        map.put("smsText", "@greetings@ We just want to remind you to settle your monthly payment by credit or debit card by clicking on the " +
                "following link: @paying_via_credit_card_sms@ If we don’t receive your payment by @paid_end_date - 1@, your service with " +
                "maids.cc will be terminated and your maid's visa will be canceled.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.EToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and sent your maid an SMS " +
                "with a link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. Please " +
                "click on the following link and sign your visa cancellation paper today @scheduled_termination_date@: @visa_cancellation_paper_link@.");
        ddMessagingList.add(map); map = new HashMap<>();

        map.put("trials", "4"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        map.put("scheduleTermCategory", DirectDebitMessagingScheduleTermCategory.GToday);
        map.put("notificationText", "Sadly, our system has automatically terminated your service and sent your maid an SMS " +
                "with a link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("smsText", "Sadly, our system has automatically terminated your service and sent your maid an SMS with a " +
                "link to sign her visa cancellation paper since we didn't receive your Monthly Bank Payment Forms. " +
                "Please ask your maid to sign her visa cancellation paper today.");
        map.put("maidText", "@maid_first_name@, Your client's contract with maids.cc has been cancelled. Please " +
                "click on the following link and sign your visa cancellation paper today @scheduled_termination_date@: @visa_cancellation_paper_link@.");
        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.MONTHLY_REMINDER);
    }

    //ACC-4715
    public void clientsPayingViaCreditCardFlowDdMessagingSetupDdSigningOfferMv() {

        List<Map<String, Object>> ddMessagingList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        map.put("trials", "1"); map.put("reminders", "1"); map.put("contractProspectTypes", "maidvisa.ae_prospect");
        map.put("clientMessagePriority", ClientMessagePriority.DO_NOT_SEND_SMS);
        map.put("notificationText", "You're currently paying your monthly payments by credit card. We just want to " +
                "let you know that you can always set up automatic payments through your personalized maids.cc app by @signing_offer_clicking_here@ .");
        map.put("smsText", "@greetings@,\n" +
                "You're currently paying your monthly payments by credit card. We just want to let you know that " +
                "you can set up automatic payments through your personalized maids.cc app by clicking on My " +
                "Payments and then on Add an Automatic Monthly Bank Payment Form. @cc_app_download_url@");

        ddMessagingList.add(map);
        createDdMessaging(ddMessagingList, DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.DD_SIGNING_OFFER);
    }

    // ACC-7930
    @PreAuthorize("hasPermission('flowProcessor', 'showSwitchClientToPayingViaCcButton')")
    @GetMapping("/showSwitchClientToPayingViaCcButton/{id}")
    public ResponseEntity<?> showSwitchClientToPayingViaCcButton(@PathVariable(name = "id") Contract contract) {
        return ResponseEntity.ok(flowProcessorService.showSwitchClientToPayingViaCcButton(contract));
    }

    // ACC-5705
    @PreAuthorize("hasPermission('flowProcessor', 'startPayingViaCreditCardFlow')")
    @GetMapping("/startPayingViaCreditCardFlow/{id}")
    @Transactional
    public ResponseEntity<?> startPayingViaCreditCardFlow(@PathVariable(name = "id") Contract contract) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        logger.info("cpt id: " + cpt.getId());
        if (cpt.getContract().isPayingViaCreditCard())
            throw new BusinessException("Contract already flagged as paying via credit card");

        if (flowProcessorEntityRepository.existsByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalse(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card, cpt.getContract()))
            throw new BusinessException("Contract has running paying via credit card flow");

        Map<String, Object> m = clientPayingViaCreditCardService.getInitialFlowPayments(cpt, true);
        if (!(boolean) m.get("startPreventCreateOtherDds")) {
            clientPayingViaCreditCardService.startPayingViaCreditCardFlow(cpt, (List<ContractPayment>) m.get("contractPayments"), false);
        }

        return ResponseEntity.ok("Done");
    }

    // ACC-7530
    @UsedBy(modules = Modules.Sales, others = Others.Erp)
    @PreAuthorize("hasPermission('flowProcessor', 'startPayingViaCreditCardFlowACC7930')")
    @GetMapping("/startPayingViaCreditCardFlowACC7930/{id}")
    @Transactional
    public ResponseEntity<?> startPayingViaCreditCardFlowACC7930(@PathVariable(name = "id") Contract contract) {

        if (contract.isPayingViaCreditCard())
            throw new BusinessException("Contract already flagged as paying via credit card");

        flowProcessorService.stopRunningFlow(contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        Map<String, Object> m = clientPayingViaCreditCardService.getInitialFlowPayments(cpt, false);
        if (!(boolean) m.get("startPreventCreateOtherDds")) {
            clientPayingViaCreditCardService.startPayingViaCreditCardFlow(cpt, (List<ContractPayment>) m.get("contractPayments"), false);
        }

        return ResponseEntity.ok("Done");
    }

    // ACC-8961
    @JwtSecured
    @EnableSwaggerMethod
    @UsedBy(others = UsedBy.Others.New_GPT)
    @GetMapping("/startPayingViaCreditCardFlowByGPT")
    public ResponseEntity<?> startPayingViaCreditCardFlowByGPT(
            @RequestParam(name = "contractId") Contract contract) {
        Map<String, Boolean> result = new HashMap<>();
        result.put("requestSuccessful", false);
        try {
            if (!flowProcessorService.showSwitchClientToPayingViaCcButton(contract, false)) {
                throw new BusinessException("Cannot be switched to paying via cc");
            } else {
                startPayingViaCreditCardFlowACC7930(contract);
                result.put("requestSuccessful", true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseEntity.ok(result);
    }

    @PreAuthorize("hasPermission('flowProcessor', 'acc6255IpamFlowMigration')")
    @GetMapping("/acc6255IpamFlowMigration")
    @Transactional
    public ResponseEntity<?> acc6255IpamFlowMigration() {
        Long lastId = -1L;
        List<FlowProcessorEntity> l;
        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        int xDaysBeforePed = Integer.parseInt(flowEventConfig.getTagValue("ipam_x_days_before_paid_end_date" ).getValue());
        int xDaysBeforeAdj = Integer.parseInt(flowEventConfig.getTagValue("ipam_x_days_before_adjusted_end_date").getValue());

        do {
            SelectQuery<FlowProcessorEntity> q = new SelectQuery<>(FlowProcessorEntity.class);
            q.filterBy("id", ">", lastId);
            q.filterBy("stopped", "=", false);
            q.filterBy("completed", "=", false);
            q.filterBy("flowEventConfig.name", "=", FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
            q.filterBy("currentSubEvent.name", "=", FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE);
            q.filterBy("lastExecutionDate", ">=", new LocalDate().dayOfMonth().withMinimumValue().toDate());
            q.filterBy("lastExecutionDate", "<=", new LocalDate().dayOfMonth().withMaximumValue().toDate());
            q.filterBy("contractPaymentTerm.contract.status", "not in", Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED));
            q.setLimit(200);
            l = q.execute();

            for (FlowProcessorEntity f : l) {
                try {
                    logger.info("flow id: " + f.getId() +
                            "; trial: " + f.getTrials() +
                            "; last execution date: " + new LocalDate(f.getLastExecutionDate()).toString("yyyy-MM-dd"));
                    LocalDate ped = new LocalDate(f.getContract().isMaidCc() ?
                            f.getContract().getPaidEndDate() : f.getContract().getAdjustedEndDate());
                    LocalDate pedMinusDays = ped.minusDays(f.getContract().isMaidCc() ? xDaysBeforePed : xDaysBeforeAdj);

                    f.setReminders(1);

                    if (new LocalDate().isBefore(pedMinusDays)) {
                        f.setTrials(1);
                    } else if (new LocalDate().toString("yyyy-MM-dd").equals(pedMinusDays.toString("yyyy-MM-dd"))) {
                        f.setTrials(2);
                        f.setLastExecutionDate(pedMinusDays.toDate());
                    } else if (new LocalDate().toString("yyyy-MM-dd").equals(pedMinusDays.plusDays(1).toString("yyyy-MM-dd"))) {
                        f.setTrials(3);
                        f.setLastExecutionDate(pedMinusDays.plusDays(1).toDate());
                    } else if (new Date().getTime() >= pedMinusDays.plusDays(2).toDate().getTime()) {
                        f.setTrials(4);
                        f.setLastExecutionDate(pedMinusDays.plusDays(2).toDate());
                    }

                    f.setLastExecutionDate(new DateTime(f.getLastExecutionDate())
                            .withHourOfDay(9).withMinuteOfHour(0).withSecondOfMinute(0)
                            .toDate());

                    flowProcessorEntityRepository.save(f);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (!l.isEmpty()) lastId = l.get(l.size() - 1).getId();

        } while (!l.isEmpty());

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('flowProcessor', 'updateIpamFlowSetupACC6255')")
    @GetMapping("/updateIpamFlowSetupACC6255")
    @Transactional
    public ResponseEntity<?> updateIpamFlowSetupACC6255() {

        Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .updateIpamFlowSetupACC6255();

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('flowProcessor', 'addAccountingPropertyForPayingViaCc')")
    @GetMapping("/addAccountingPropertyForPayingViaCc")
    public ResponseEntity<?> addAccountingPropertyForPayingViaCc() {

        Page<Contract> p;
        Long lastId = -1L;
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);

        do {

            p = contractRepository.findContractPayingViaCcForAddAccountingProperty(lastId, PageRequest.of(0, 200));

            p.getContent().forEach(c -> {
                try {
                    Date d = null;
                    Long revision = contractRepository.findContractLastRevisionNotFlaggedASPayingViaCc(c.getId());
                    if (revision != null) {
                        d = contractRepository.findContractFirstRevisionFlaggedASPayingViaCcAfterRevision(c.getId(), revision);
                    }

                    if (d == null) {
                        logger.info("contract id: " + c.getId() + " couldn't find a date");
                        return;
                    }

                    logger.info("contract id: " + c.getId() + "; date: " + new LocalDate(d).toString("yyyy-MM-dd HH:mm:ss"));

                    AccountingEntityProperty a = new AccountingEntityProperty();
                    a.setOrigin(c);
                    a.setKey(Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE);
                    a.setValue(DateUtil.formatDateDashedWithTimeV3(d));
                    accountingEntityPropertyRepository.save(a);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }

        } while (!p.getContent().isEmpty());


        return ResponseEntity.ok("Done");
    }


    @PreAuthorize("hasPermission('flowProcessor', 'addAccountingPropertyForPayingViaCcFromFile')")
    @PostMapping("/addAccountingPropertyForPayingViaCcFromFile")
    public ResponseEntity<?> addAccountingPropertyForPayingViaCcFromFile(MultipartFile file) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) return new ResponseEntity<>("No sheet found", HttpStatus.BAD_REQUEST);

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);

        for(Row row : sheet) {
            try {
                if (row.getRowNum() == 0) continue;
                logger.info("Row Num: " + row.getRowNum());
                Long contractId = (long) row.getCell(0).getNumericCellValue();
                logger.info("contract id: " + contractId);
                Contract contract = contractRepository.findOne(contractId);
                if (contract == null) {
                    logger.log(Level.SEVERE, "contract id: {0} not found", contractId);
                    continue;
                }

                DateTime d =  DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")
                        .parseDateTime(row.getCell(2).getStringCellValue());

                AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                        Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE, contract);
                if (a == null) {
                    a = new AccountingEntityProperty();
                    a.setOrigin(contract);
                    a.setKey(Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE);
                }
                a.setValue(DateUtil.formatDateDashedWithTimeV3(d.toDate()));
                accountingEntityPropertyRepository.save(a);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return ResponseEntity.ok("Done");
    }

    // ACC-6624
    @PreAuthorize("hasPermission('flowProcessor', 'updateOnlineCreditCardPaymentRemindersAcc6624')")
    @GetMapping("/updateOnlineCreditCardPaymentRemindersAcc6624")
    public ResponseEntity<?> updateOnlineCreditCardPaymentRemindersAcc6624() {

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(
                FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        if (flowEventConfig.getCancellationReason() == null) {
            flowEventConfig.setCancellationReason(PicklistHelper.getItem(
                    AccountingModule.PICKLIST_TERMINATION_REASON_LIST, Contract.ONLINE_REMINDER_TERMINATION_REASON));
            flowEventConfigRepository.save(flowEventConfig);
        }

        Map<String, Object> subEventMap = new HashMap<>();
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS);
        subEventMap.put("requiredAction", RequiredAction.ONLINE_CREDIT_CARD_PAYMENT_RECEIVED);
        subEventMap.put("maxTrials", 1);
        subEventMap.put("maxReminders", 6);
        subEventMap.put("terminateOnMaxTrials", false);
        subEventMap.put("terminateOnMaxReminders", true);
        FlowSubEventConfig flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        List<Map<String, Integer>> values = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 2); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 3); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 4); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 5); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 6); map.put("period", 24); values.add(map); map = new HashMap<>();
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                FlowSubEventConfig.FlowSubEventName.PENDING_PAYMENT, flowEventConfig);

        flowSubEventConfig.setMaxReminders(2);
        flowSubEventConfigRepository.save(flowSubEventConfig);

        values = new ArrayList<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 2); map.put("period", 24); values.add(map); map = new HashMap<>();
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        values = new ArrayList<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 2); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 3); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 4); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 5); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 6); map.put("reminder", 1); map.put("period", 24); values.add(map);
        flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
        flowSubEventConfig.setMaxTrials(6);
        flowSubEventConfigRepository.save(flowSubEventConfig);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
        flowSubEventConfig.setMaxTrials(6);
        flowSubEventConfigRepository.save(flowSubEventConfig);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        return ResponseEntity.ok("Done");
    }

    // ACC-6804
    @Transactional
    @GetMapping("/incompleteFlowMissingBankInfoFlowSetup")
    public ResponseEntity<?> incompleteFlowMissingBankInfoFlowSetup() {

        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("stopMessagesOnContractCancellation", true);
        eventMap.put("stopTodosOnContractCancellation", true);
        eventMap.put("closeToDosUponCompletion", false);
        eventMap.put("maxFlowRuns", 1);
        eventMap.put("cancellationReason", flowProcessorService.createOrFetchCancellationReason(
                "signature_collection_flow_max_trials_reached", "Signature Collection Flow Max Trials Reached"));
        FlowEventConfig flowEventConfig = flowProcessorService.createOrFetchFlowEventConfig(
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO, eventMap);

        Map<String, Object> subEventMap = new HashMap<>();
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.MISSING_BANK_INFO);
        subEventMap.put("requiredAction", RequiredAction.CLIENT_PROVIDES_MISSING_BANK_INFO);
        subEventMap.put("maxTrials", 2);
        subEventMap.put("maxReminders", 3);
        subEventMap.put("terminateOnMaxTrials", true);
        subEventMap.put("terminateOnMaxReminders", true);
        FlowSubEventConfig flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        List<Map<String, Integer>> values = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 2); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 3); map.put("period", 24); values.add(map);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        {
            PicklistItem i = PicklistHelper.getItemNoException("PICKLIST_COLLECTION_FLOW_TYPE","missing/wrong_document_flow");
            if (i != null && !i.getName().equals("Incomplete flow / Data entry rejection")) {
                i.setName("Incomplete flow / Data entry rejection");
                picklistItemRepository.save(i);
            }
        }

        {
            PicklistItem p = PicklistHelper.getItemNoException("PICKLIST_COLLECTION_FLOW_TYPE", "incomplete_flow_missing_bank_info");
            if (p == null) {
                p = new PicklistItem();
                p.setCode("incomplete_flow_missing_bank_info");
                p.setName("Incomplete flow / Missing bank info");
                p.setList(Setup.getRepository(PicklistRepository.class).findByCode("PICKLIST_COLLECTION_FLOW_TYPE"));
                picklistItemRepository.save(p);
            }
        }

        {
            AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
            AccountingEntityProperty a = accountingEntityPropertyRepository
                    .findByKeyAndIsDeletedFalse("signature_collection_flow_job_last_check_date");
            if (a != null) {
                accountingEntityPropertyRepository.delete(a);
            }
        }

        {
            if (flowEventConfig.getTags().isEmpty()) {
                List<Tag> tags = tagRepository.findByNameIgnoreCase("defaultDDSendTime:09:00:00");
                if (!tags.isEmpty()) {
                    flowEventConfig = flowEventConfigRepository.findOne(flowEventConfig.getId());
                    flowEventConfig.getTags().add(tags.get(0));
                    flowEventConfigRepository.save(flowEventConfig);
                }
            }
        }

        return ResponseEntity.ok("Done");
    }

    @GetMapping("/migrateOldIncompleteFlowMissingBankInfoFlowSetup")
    public ResponseEntity<?> migrateOldIncompleteFlowMissingBankInfoFlowSetup() {

        Long lastId = -1L;
        List<SignatureCollectionFlowToDo> l;
        SignatureCollectionFlowToDoRepository signatureCollectionFlowToDoRepository = Setup.getRepository(SignatureCollectionFlowToDoRepository.class);

        do {
            SelectQuery<SignatureCollectionFlowToDo> q = new SelectQuery<>(SignatureCollectionFlowToDo.class);
            q.filterBy("isActive", "=", true);
            q.filterBy("id", ">", lastId);
            q.setLimit(200);
            l = q.execute();

            l.forEach(f -> {
                try {
                    logger.info("contract id: " + f.getContract().getId());

                    Map<String, Object> map = new HashMap<>();
                    map.put("trials", 1);
                    map.put("reminders", 0);
                    map.put("lastExecutionDate", new Date());

                    flowProcessorService.createFlowProcessor(
                            FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO,
                            FlowSubEventConfig.FlowSubEventName.MISSING_BANK_INFO,
                            f.getContract().getActiveContractPaymentTerm(),
                            map);

                    f.setActive(false);
                    signatureCollectionFlowToDoRepository.save(f);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            });


            if (!l.isEmpty()) {
                lastId = l.get(l.size() - 1).getId();
            }

        } while (!l.isEmpty());

        return ResponseEntity.ok("Done");
    }

    @Transactional
    @PreAuthorize("hasPermission('flowProcessor', 'paymentExpiryFlowSetup')")
    @GetMapping("/paymentExpiryFlowSetup")
    public ResponseEntity<?> paymentExpiryFlowSetup() {
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("stopMessagesOnContractCancellation", true);
        eventMap.put("stopTodosOnContractCancellation", true);
        eventMap.put("closeToDosUponCompletion", false);
        eventMap.put("maxFlowRuns", 1);
        eventMap.put("cancellationReason", flowProcessorService.createOrFetchCancellationReason(
                "Service_not_needed_kids_are_grown_up_smaller_house", "Service not needed anymore"));
        FlowEventConfig flowEventConfig = flowProcessorService.createOrFetchFlowEventConfig(
                FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW, eventMap);

        Map<String, Object> subEventMap = new HashMap<>();
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.GENERATE_DDS_AFTER_PAID_END_DATE);
        subEventMap.put("requiredAction", RequiredAction.CLIENT_PROVIDES_SIGNATURE);
        subEventMap.put("maxTrials", 1);
        subEventMap.put("maxReminders", 1);
        subEventMap.put("terminateOnMaxTrials", true);
        subEventMap.put("terminateOnMaxReminders", true);
        flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('flowProcessor', 'dataCorrectionStopPayingViaCCFlowACC8781')")
    @GetMapping("/dataCorrectionStopPayingViaCCFlowACC8781")
    public ResponseEntity<?> dataCorrectionStopPayingViaCCFlowACC8781() {
        return ResponseEntity.ok(flowProcessorService.dataCorrectionStopPayingViaCCFlowForIPAMFlowsACC8781());
    }

    @PreAuthorize("hasPermission('flowProcessor', 'reactivateCollectionFlow')")
    @GetMapping("/reactivateCollectionFlow/{id}")
    @Transactional
    public ResponseEntity<?> reactivateCollectionFlow(@PathVariable(name = "id") Contract contract) throws Exception {

        List<FlowProcessorEntity> l = flowProcessorEntityRepository.findAllStoppedFlowsToReactivateCollectionFlowByContract(contract);
        for (FlowProcessorEntity f : l) {
            logger.info("flow id: " + f.getId() +
                    "; event: " + f.getFlowEventConfig().getName() +
                    "; sub event: " + f.getCurrentSubEvent().getName());

            // ACC-8954
            if (f.getFlowEventConfig().getName().equals(FlowEventConfig.FlowEventName.EXTENSION_FLOW)) {
                f.setCausedTermination(false);
                f.setStoppedDueContractTerminated(false);
                flowProcessorEntityRepository.save(f);

                f = flowProcessorEntityRepository.findOne(Utils.parseValue(f.getAdditionalValue("flowCausedTerminationId"), Long.class));
                logger.info("flow Caused Termination Id: " + f.getId() +
                        "; event: " + f.getFlowEventConfig().getName() +
                        "; sub event: " + f.getCurrentSubEvent().getName());
            }

            f.setStoppedDueContractTerminated(false);
            f.setCausedTermination(false);
            f = flowProcessorEntityRepository.save(f);

            switch (f.getFlowEventConfig().getName()) {
                case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                    if (!flowProcessorService.clientProvidesSignatureAndBankInfo(f.getContract(), f.getCreationDate(), false, false)) {
                        Setup.getApplicationContext()
                                .getBean(AfterCashFlowService.class)
                                .reactivateFlow(f);
                    }
                    break;
                case CLIENTS_PAYING_VIA_Credit_Card:
                    if (!contract.isPayingViaCreditCard()) break;
                    if (Arrays.asList(FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA,
                                    FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB)
                            .contains(f.getCurrentSubEvent().getName())) {
                        clientPayingViaCreditCardService.reactivateInitialFlowViaStoppedFlow(f);
                    } else if (f.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER)) {
                        clientPayingViaCreditCardService.switchToMonthReminder(f);
                    }
                    break;
                case ONLINE_CREDIT_CARD_PAYMENT_REMINDERS:
                    Setup.getApplicationContext()
                            .getBean(UnpaidOnlineCreditCardPaymentService.class)
                                    .reactivateReminderFlowAfterReActiveContract(f.getContractPaymentConfirmationToDo());
                    break;
            }
        }

        // Bouncing Flow
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        List<Payment> payments = paymentRepository.findAllNonMonthlyBouncedPaymentByContractId(contract.getId());
        if (!payments.isEmpty()) {
            Integer maxTrial = contract.isMaidVisa() ?
                    Integer.parseInt(Setup.getParameter(
                            Setup.getCurrentModule(), AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV)) :
                    Integer.parseInt(Setup.getParameter(
                            Setup.getCurrentModule(), AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC));

            Integer difference = Integer.valueOf(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DIFFERENCE_RESET_TRIAL_BOUNCED_PAYMENT));
            PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
            for (Payment payment : payments) {
                logger.info("payment id: " + payment.getId());
                if(payment.getTrials() > maxTrial - difference) {
                    payment.setTrials(maxTrial - difference);
                }
                payment.setReminder(0);
                payment.setContractScheduleDateOfTermination(null);
                paymentService.updatePaymentSilent(payment);
            }
        }

        // DDs Regeneration
        Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .reactivateDDsFlowAfterReactivateContract(contract.getActiveContractPaymentTerm());

        return ResponseEntity.ok("Done");
    }

    @Transactional
    @PreAuthorize("hasPermission('flowProcessor', 'addSubEventToPayingViaCCFlow')")
    @GetMapping("/addSubEventToPayingViaCCFlow")
    public ResponseEntity<?> addSubEventToPayingViaCCFlow() {

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        Map<String, Object> subEventMap = new HashMap<>();
        List<Map<String, Integer>> values = new ArrayList<>();

        // TOKEN_DELETED
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.TOKEN_DELETED);
        subEventMap.put("requiredAction", RequiredAction.ONLINE_CREDIT_CARD_PAYMENT_RECEIVED);
        subEventMap.put("maxTrials", 1);
        subEventMap.put("maxReminders", 1);
        subEventMap.put("terminateOnMaxTrials", false);
        subEventMap.put("terminateOnMaxReminders", false);
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        FlowSubEventConfig flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        // INSUFFICIENT_FUNDS
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.INSUFFICIENT_FUNDS);
        subEventMap.put("maxTrials", 4);
        subEventMap.put("terminateOnMaxTrials", true);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        map.put("trial", 2); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 3); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 4); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        // EXCEEDING_DAILY_LIMITS
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.EXCEEDING_DAILY_LIMITS);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        // ACCOUNT_ISSUE
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.ACCOUNT_ISSUE);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        // CC_OTHER_ISSUES
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.CC_OTHER_ISSUES);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        // EXPIRED_CARD
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD);
        flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        PicklistItem picklistItem = PicklistHelper.getItemNoException(AccountingModule.PICKLIST_CREDIT_CARD_ERROR_CODES, "305");
        if (picklistItem == null) {
            Picklist picklist = new Picklist(AccountingModule.PICKLIST_CREDIT_CARD_ERROR_CODES, "credit card error codes");
            picklist.addItem(new PicklistItem(picklist, "305", "305"));
            picklist.addItem(new PicklistItem(picklist, "321", "321"));
            picklist.addItem(new PicklistItem(picklist, "source_id_required", "source_id_required"));

            Setup.getRepository(PicklistRepository.class).save(picklist);
        }

        return ResponseEntity.ok("Done");
    }

    @GetMapping("/updateRecurringFailureFlowsConfig")
    @Transactional
    public ResponseEntity<?> updateRecurringFailureFlowsConfig() {

        List<Map<String, Integer>> values = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 2); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 3); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 5); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 6); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 7); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 8); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 9); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 10); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 11); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 12); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 13); map.put("reminder", 1); map.put("period", 24); values.add(map);

        // INSUFFICIENT_FUNDS
        FlowSubEventConfig subEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                FlowSubEventConfig.FlowSubEventName.INSUFFICIENT_FUNDS, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        subEventConfig.setMaxTrials(13);
        flowSubEventConfigRepository.save(subEventConfig);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);

        // EXCEEDING_DAILY_LIMITS
        subEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                FlowSubEventConfig.FlowSubEventName.EXCEEDING_DAILY_LIMITS, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        subEventConfig.setMaxTrials(13);
        flowSubEventConfigRepository.save(subEventConfig);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);

        // ACCOUNT_ISSUE
        subEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                FlowSubEventConfig.FlowSubEventName.ACCOUNT_ISSUE, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        subEventConfig.setMaxTrials(13);
        flowSubEventConfigRepository.save(subEventConfig);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);

        // CC_OTHER_ISSUES
        subEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                FlowSubEventConfig.FlowSubEventName.CC_OTHER_ISSUES, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        subEventConfig.setMaxTrials(13);
        flowSubEventConfigRepository.save(subEventConfig);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);


        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        // INSUFFICIENT_FUNDS_MV
        Map<String, Object> subEventMap = new HashMap<>();
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.INSUFFICIENT_FUNDS_MV);
        subEventMap.put("requiredAction", RequiredAction.ONLINE_CREDIT_CARD_PAYMENT_RECEIVED);
        subEventMap.put("maxTrials", 22);
        subEventMap.put("maxReminders", 1);
        subEventMap.put("terminateOnMaxTrials", true);
        subEventMap.put("terminateOnMaxReminders", false);

        subEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        map = new HashMap<>(); map.put("trial", 14); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 15); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 16); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 17); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 18); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 19); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 20); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 21); map.put("reminder", 1); map.put("period", 24); values.add(map);
        map = new HashMap<>(); map.put("trial", 22); map.put("reminder", 1); map.put("period", 24); values.add(map);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);

        // EXCEEDING_DAILY_LIMITS_MV
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.EXCEEDING_DAILY_LIMITS_MV);
        subEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);

        // ACCOUNT_ISSUE_MV
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.ACCOUNT_ISSUE_MV);
        subEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);

        // CC_OTHER_ISSUES_MV
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.CC_OTHER_ISSUES_MV);
        subEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);
        flowProcessorService.createFlowProgressPeriods(values, subEventConfig);


        return ResponseEntity.ok("Done");
    }

    @GetMapping("/extensionFlowSetup")
    @Transactional
    public ResponseEntity<?> extensionFlowSetup() {
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("stopMessagesOnContractCancellation", true);
        eventMap.put("stopTodosOnContractCancellation", true);
        eventMap.put("closeToDosUponCompletion", true);
        eventMap.put("maxFlowRuns", 1);
        eventMap.put("cancellationReason", flowProcessorService.createOrFetchCancellationReason(
                Contract.DUE_EXTENSION_FLOW_TERMINATION_REASON, "Due Extension Flow"));
        FlowEventConfig flowEventConfig = flowProcessorService.createOrFetchFlowEventConfig(FlowEventConfig.FlowEventName.EXTENSION_FLOW, eventMap);

        Map<String, Object> subEventMap = new HashMap<>();
        subEventMap.put("flowEventConfig", flowEventConfig);
        subEventMap.put("flowSubEventName", FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_EXTENSION);
        subEventMap.put("requiredAction", RequiredAction.ONLINE_CREDIT_CARD_PAYMENT_RECEIVED);
        subEventMap.put("maxTrials", 7);
        subEventMap.put("maxReminders", 2);
        subEventMap.put("terminateOnMaxTrials", true);
        subEventMap.put("terminateOnMaxReminders", false);
        FlowSubEventConfig flowSubEventConfig = flowProcessorService.createOrFetchFlowSubEventConfig(subEventMap);

        List<Map<String, Integer>> values = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 2); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 2); map.put("reminder", 2); map.put("period", 2); values.add(map); map = new HashMap<>();
        map.put("trial", 3); map.put("reminder", 1); map.put("period", 22); values.add(map); map = new HashMap<>();
        map.put("trial", 4); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 4); map.put("reminder", 2); map.put("period", 2); values.add(map); map = new HashMap<>();
        map.put("trial", 5); map.put("reminder", 1); map.put("period", 22); values.add(map); map = new HashMap<>();
        map.put("trial", 6); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 6); map.put("reminder", 2); map.put("period", 2); values.add(map); map = new HashMap<>();
        map.put("trial", 7); map.put("reminder", 1); map.put("period", 23); values.add(map);
        flowProcessorService.createFlowProgressPeriods(values, flowSubEventConfig);

        PicklistItem p = PicklistHelper.getItemNoException(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE, "extension_flow");
        if (p == null) {
            p = new PicklistItem();
            p.setCode("extension_flow");
            p.setName("Extension Flow");
            p.setList(Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE));
            Setup.getRepository(PicklistItemRepository.class).save(p);
        }

        if (flowEventConfig.getTags().isEmpty()) {
            List<Tag> tags = tagRepository.findByNameIgnoreCase("defaultDDSendTime:09:00:00");
            if (!tags.isEmpty()) {
                flowEventConfig = flowEventConfigRepository.findOne(flowEventConfig.getId());
                flowEventConfig.getTags().add(tags.get(0));
                flowEventConfigRepository.save(flowEventConfig);
            }
        }

        // Create new Termination reasons for Ipam and Paying via cc
        // Ipam
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
        flowEventConfig.setCancellationReason(flowProcessorService.createOrFetchCancellationReason(
                Contract.DUE_IPAM_FLOW_TERMINATION_REASON, "Due IPAM Flow"));
        flowEventConfigRepository.save(flowEventConfig);

        // Paying via CC
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
        flowEventConfig.setCancellationReason(flowProcessorService.createOrFetchCancellationReason(
                Contract.DUE_PAYING_VIA_CC_FLOW_TERMINATION_REASON, "Due Paying via CC Flow"));
        flowEventConfigRepository.save(flowEventConfig);
        return okResponse();
    }
}