package com.magnamedia.controller;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.service.adcb.ApiService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @creation_date 4/8/2025
 * */
@RestController
@RequestMapping("/adcb")
public class AdcbController {

    @Autowired
    private ApiService apiService;

    /**
     * Generic API endpoint for testing ADCB bank APIs
     * 
     * @param request Contains endpoint, method, queryParams, body, and headers
     * @return ResponseEntity with the API response from ADCB
     */
    @NoPermission
    @PostMapping("/generic-api")
    public ResponseEntity<Map<String, Object>> callGenericApi(@RequestBody AdcbGenericApiRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // Call the generic API service
            ApiService.GenericApiResponse apiResponse = apiService.callGenericApi(request);
            
            // Build success response
            response.put("status", "success");
            
            // Build request details
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("fullUrl", request.getFullUrl());
            requestDetails.put("method", request.getMethod());
            requestDetails.put("queryParams", request.getQueryParams() != null ? request.getQueryParams() : new HashMap<>());
            requestDetails.put("body", request.getBody() != null ? request.getBody() : "null");
            requestDetails.put("requestUrl", apiResponse.getRequestUrl());
            response.put("request", requestDetails);
            
            // Build response details
            Map<String, Object> responseDetails = new HashMap<>();
            responseDetails.put("statusCode", apiResponse.getStatusCode());
            responseDetails.put("statusMessage", apiResponse.getStatusMessage());
            responseDetails.put("body", apiResponse.getResponseBody());
            responseDetails.put("method", apiResponse.getRequestMethod());
            responseDetails.put("headers", apiResponse.getResponseHeaders());
            response.put("response", responseDetails);
            
            // Return appropriate HTTP status based on ADCB response
            HttpStatus httpStatus = apiService.getHttpStatusFromCode(apiResponse.getStatusCode());
            return ResponseEntity.status(httpStatus).body(response);
            
        } catch (Exception e) {
            // Handle any exceptions
            response.put("status", "error");
            response.put("error", "Failed to call ADCB API: " + e.getMessage());
            response.put("exception", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @Getter
    @Setter
    public static class AdcbGenericApiRequest {
        private boolean useSSL = false;
        private String fullUrl;
        private String method;
        private Map<String, String> queryParams;
        private Object body;
        private Map<String, String> headers;
        private HashMap<String, Object> accessTokenData;
    }
}